<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>安全验证系统</title>
    <style>
        /* 基础重置 - 确保多浏览器一致性 */
        html, body {
            margin: 0;
            padding: 0;
            height: 100%;
            overflow: hidden;
            font-family: <PERSON><PERSON>, "Microsoft YaHei", sans-serif;
        }
        
        body {
            background-color: #0a192f;
            color: #e6f1ff;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        /* 蓝色数字雨效果 */
        .matrix-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 0;
            opacity: 0.15;
            pointer-events: none;
        }
        
        /* 蓝色主题验证窗口 */
        .auth-box {
            position: relative;
            width: 320px;
            padding: 30px;
            background-color: rgba(16, 42, 87, 0.85);
            border: 1px solid rgba(100, 200, 255, 0.3);
            border-radius: 5px;
            box-shadow: 0 0 15px rgba(100, 200, 255, 0.4),
                        inset 0 0 10px rgba(100, 200, 255, 0.2);
            z-index: 1;
        }
        
        .auth-title {
            text-align: center;
            margin-bottom: 25px;
            font-size: 20px;
            text-shadow: 0 0 8px rgba(100, 200, 255, 0.6);
        }
        
        .password-group {
            margin-bottom: 20px;
        }
        
        .password-label {
            display: block;
            margin-bottom: 8px;
            font-size: 14px;
            color: #a8b2d1;
        }
        
        .password-input {
            width: 100%;
            padding: 12px 15px;
            background-color: rgba(10, 25, 47, 0.8);
            border: 1px solid rgba(100, 200, 255, 0.3);
            color: #e6f1ff;
            border-radius: 4px;
            font-size: 16px;
            outline: none;
            box-sizing: border-box;
        }
        
        .password-input:focus {
            border-color: #64ffda;
            box-shadow: 0 0 8px rgba(100, 255, 218, 0.5);
        }
        
        .submit-btn {
            width: 100%;
            padding: 12px;
            background-color: #1e90ff;
            border: none;
            color: white;
            font-weight: bold;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s;
        }
        
        .submit-btn:hover {
            background-color: #0077ff;
            box-shadow: 0 0 10px rgba(30, 144, 255, 0.7);
        }
        
        .status-text {
            margin-top: 15px;
            min-height: 20px;
            text-align: center;
            font-size: 14px;
        }
        
        /* 加载动画 */
        @keyframes fadePulse {
            0%, 100% { opacity: 0.6; }
            50% { opacity: 1; }
        }
        
        .loading {
            animation: fadePulse 1.5s infinite;
            color: #64ffda;
        }
        
        .success {
            color: #64ffda;
        }
        
        .error {
            color: #ff5555;
        }
    </style>
</head>
<body>
    <!-- 蓝色数字雨背景 -->
    <canvas id="matrix" class="matrix-bg"></canvas>
    
    <!-- 验证窗口 -->
    <div class="auth-box">
        <h2 class="auth-title">加密安全验证</h2>
        
        <div class="password-group">
            <label class="password-label">请输入访问密码</label>
            <input type="password" class="password-input" id="passwordInput" placeholder="输入密码" autocomplete="off" autofocus>
        </div>
        
        <button class="submit-btn" id="submitBtn">验证</button>
        
        <div class="status-text" id="statusText">等待验证...</div>
    </div>

    <script>
        // 蓝色数字雨效果
        function initMatrix() {
            var canvas = document.getElementById('matrix');
            if (!canvas.getContext) return;
            
            var ctx = canvas.getContext('2d');
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
            
            var chars = "01";
            chars = chars.split("");
            
            var font_size = 14;
            var columns = canvas.width / font_size;
            var drops = [];
            
            for (var x = 0; x < columns; x++)
                drops[x] = 1; 
            
            function draw() {
                ctx.fillStyle = "rgba(10, 25, 47, 0.05)";
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                
                ctx.fillStyle = "#64ffda";
                ctx.font = font_size + "px arial";
                
                for (var i = 0; i < drops.length; i++) {
                    var text = chars[Math.floor(Math.random() * chars.length)];
                    ctx.fillText(text, i * font_size, drops[i] * font_size);
                    
                    if (drops[i] * font_size > canvas.height && Math.random() > 0.975)
                        drops[i] = 0;
                    
                    drops[i]++;
                }
            }
            
            setInterval(draw, 33);
        }
        
        // 页面加载完成后初始化
        window.onload = function() {
            initMatrix();
            
            // 自动聚焦密码输入框
            document.getElementById('passwordInput').focus();
            
            // 验证函数
            function verifyPassword() {
                var password = document.getElementById('passwordInput').value;
                var statusText = document.getElementById('statusText');
                
                statusText.textContent = "验证中...";
                statusText.className = "status-text loading";
                
                // 模拟验证过程
                setTimeout(function() {
                    if (password && password.length >= 8) {
                        statusText.textContent = "✓ 验证成功，正在进入系统...";
                        statusText.className = "status-text success";
                        
                        // 验证成功后的操作 - 这里可以替换为实际跳转逻辑
                        setTimeout(function() {
                            // window.location.href = "secured-page.html";
                            console.log("验证成功，执行后续操作");
                        }, 1000);
                    } else {
                        statusText.textContent = "✗ 密码无效或已过期";
                        statusText.className = "status-text error";
                    }
                }, 800);
            }
            
            // 按钮点击事件
            document.getElementById('submitBtn').addEventListener('click', verifyPassword);
            
            // 回车键提交
            document.getElementById('passwordInput').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    verifyPassword();
                }
            });
            
            // 窗口大小变化时重绘背景
            window.addEventListener('resize', function() {
                var canvas = document.getElementById('matrix');
                if (canvas) {
                    canvas.width = window.innerWidth;
                    canvas.height = window.innerHeight;
                }
            });
        };
    </script>
</body>
</html>