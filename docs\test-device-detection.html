<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设备检测测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .info-item {
            margin: 10px 0;
            padding: 10px;
            background: #f8f9fa;
            border-left: 4px solid #007bff;
        }
        .device-key {
            font-weight: bold;
            color: #007bff;
        }
        .config-section {
            margin-top: 30px;
            padding: 20px;
            background: #e9ecef;
            border-radius: 5px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .test-config {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>设备检测测试页面</h1>
        
        <div id="device-info">
            <h2>设备信息</h2>
            <div id="info-content">正在加载设备信息...</div>
        </div>
        
        <div class="config-section">
            <h2>测试配置</h2>
            <div class="test-config">
                <p><strong>注意：</strong>这是一个测试页面，用于验证设备检测功能。</p>
                <p>您可以使用浏览器的设备模拟功能来测试不同设备类型的检测结果。</p>
            </div>
            
            <button onclick="refreshDeviceInfo()">刷新设备信息</button>
            <button onclick="testRedirect()">测试跳转功能</button>
            <button onclick="showUserAgent()">显示User Agent</button>
        </div>
        
        <div id="test-results" class="config-section" style="display: none;">
            <h2>测试结果</h2>
            <div id="results-content"></div>
        </div>
    </div>

    <!-- 加载设备检测模块 -->
    <script src="assets/js/device-detection.js"></script>
    
    <script>
        // 测试配置
        const testConfig = {
            enabled: true,
            ios_phone: 'https://example.com/ios-phone',
            ios_tablet: 'https://example.com/ios-tablet',
            android_phone: 'https://example.com/android-phone',
            android_tablet: 'https://example.com/android-tablet',
            android_desktop: 'https://example.com/android-desktop',
            windows_phone: 'https://example.com/windows-phone',
            windows_tablet: 'https://example.com/windows-tablet',
            windows_desktop: 'https://example.com/windows-desktop',
            macos_desktop: 'https://example.com/macos-desktop',
            linux_desktop: 'https://example.com/linux-desktop',
            unknown: 'https://example.com/unknown'
        };

        function initTest() {
            if (typeof window.deviceRedirectManager === 'undefined') {
                setTimeout(initTest, 100);
                return;
            }
            
            // 设置测试配置
            window.deviceRedirectManager.setRedirectConfig(testConfig);
            window.deviceRedirectManager.setDebugMode(true);
            
            // 显示设备信息
            refreshDeviceInfo();
        }

        function refreshDeviceInfo() {
            if (typeof window.deviceRedirectManager === 'undefined') {
                document.getElementById('info-content').innerHTML = '<p style="color: red;">设备检测模块未加载</p>';
                return;
            }
            
            const deviceInfo = window.deviceRedirectManager.getDeviceInfo();
            const redirectUrl = testConfig[deviceInfo.deviceKey] || '未配置';
            
            const html = `
                <div class="info-item">
                    <strong>设备类型键名：</strong> <span class="device-key">${deviceInfo.deviceKey}</span>
                </div>
                <div class="info-item">
                    <strong>设备友好名称：</strong> ${deviceInfo.deviceName}
                </div>
                <div class="info-item">
                    <strong>是否移动设备：</strong> ${deviceInfo.isMobile ? '是' : '否'}
                </div>
                <div class="info-item">
                    <strong>是否平板设备：</strong> ${deviceInfo.isTablet ? '是' : '否'}
                </div>
                <div class="info-item">
                    <strong>是否桌面设备：</strong> ${deviceInfo.isDesktop ? '是' : '否'}
                </div>
                <div class="info-item">
                    <strong>是否苹果设备：</strong> ${deviceInfo.isAppleDevice ? '是' : '否'}
                </div>
                <div class="info-item">
                    <strong>是否支持触摸：</strong> ${deviceInfo.isTouch ? '是' : '否'}
                </div>
                <div class="info-item">
                    <strong>平台信息：</strong> ${deviceInfo.platform}
                </div>
                <div class="info-item">
                    <strong>配置的跳转URL：</strong> ${redirectUrl}
                </div>
            `;
            
            document.getElementById('info-content').innerHTML = html;
        }

        function testRedirect() {
            if (typeof window.deviceRedirectManager === 'undefined') {
                alert('设备检测模块未加载');
                return;
            }
            
            const deviceInfo = window.deviceRedirectManager.getDeviceInfo();
            const redirectUrl = testConfig[deviceInfo.deviceKey];
            
            if (!redirectUrl) {
                alert(`当前设备类型 "${deviceInfo.deviceName}" 未配置跳转URL`);
                return;
            }
            
            const confirmed = confirm(`即将跳转到：${redirectUrl}\n\n确定要继续吗？`);
            if (confirmed) {
                // 这里不实际跳转，只是显示结果
                showTestResult(`模拟跳转到：${redirectUrl}`);
            }
        }

        function showUserAgent() {
            const userAgent = navigator.userAgent;
            const platform = navigator.platform;
            
            showTestResult(`
                <strong>User Agent:</strong><br>
                ${userAgent}<br><br>
                <strong>Platform:</strong><br>
                ${platform}
            `);
        }

        function showTestResult(content) {
            document.getElementById('results-content').innerHTML = content;
            document.getElementById('test-results').style.display = 'block';
        }

        // 初始化测试
        document.addEventListener('DOMContentLoaded', initTest);
        initTest(); // 立即尝试初始化
    </script>
</body>
</html>
