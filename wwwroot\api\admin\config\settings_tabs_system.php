<?php
/**
 * 管理后台系统设置选项卡配置
 *
 * 该文件定义了管理后台系统设置页面的选项卡、分组和字段结构
 * 仅负责UI展示和交互，不包含具体参数的默认值
 */

return [
    // 系统设置选项卡
    [
        'name' => 'system',
        'title' => '系统设置',
        'icon' => 'fas fa-cogs',
        'groups' => [
            // 数据库设置组
            [
                'name' => 'database',
                'title' => '数据库设置',
                'settings_path' => '', // 特殊标记，表示存储在根级别
                'fields' => [
                    [
                        'name' => 'db_path',
                        'label' => '数据库路径',
                        'type' => 'text',
                        'required' => true,
                        'placeholder' => 'database/nbbrj.db',
                        'description' => '相对于网站根目录的路径，用于存储数据库文件'
                    ]
                ]
            ],
            // 文件存储设置组
            [
                'name' => 'storage',
                'title' => '文件存储设置',
                'settings_path' => 'upload', // 对应Settings类中的路径
                'fields' => [
                    [
                        'name' => 'upload.software_package_path',
                        'label' => '软件包上传路径',
                        'type' => 'text',
                        'required' => true,
                        'placeholder' => 'uploads/software/package/',
                        'description' => '相对于网站根目录的路径，用于存储上传的软件包文件'
                    ],
                    [
                        'name' => 'upload.software_icon_path',
                        'label' => '软件图标上传路径',
                        'type' => 'text',
                        'required' => true,
                        'placeholder' => 'uploads/software/icon/',
                        'description' => '相对于网站根目录的路径，用于存储上传的软件图标'
                    ],
                    [
                        'name' => 'upload.site_images_path',
                        'label' => '站点图片上传路径',
                        'type' => 'text',
                        'required' => true,
                        'placeholder' => 'uploads/site/',
                        'description' => '相对于网站根目录的路径，用于存储上传的站点图片（如二维码等）'
                    ]
                ]
            ],
            // 安全设置组
            [
                'name' => 'security_settings',
                'title' => '安全设置',
                'settings_path' => 'security', // 对应Settings类中的路径
                'fields' => [
                    [
                        'name' => 'security.access_password',
                        'label' => '访问密码',
                        'type' => 'password',
                        'placeholder' => '留空表示不需要验证',
                        'description' => '设置首页访问密码，留空表示不需要验证。设置后用户需要输入正确密码才能访问首页内容。'
                    ]
                ]
            ],

            // 调试设置组
            [
                'name' => 'debug_settings',
                'title' => '调试设置',
                'settings_path' => '', // 特殊标记，表示存储在根级别
                'fields' => [
                    [
                        'name' => 'debug',
                        'label' => '调试模式',
                        'type' => 'checkbox',
                        'default' => false,
                        'description' => '启用调试模式，在前台显示调试信息'
                    ]
                ]
            ]
        ]
    ]
];
