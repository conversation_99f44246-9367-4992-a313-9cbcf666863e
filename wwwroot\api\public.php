<?php
/**
 * 公共API验证文件
 * 此文件应被所有公共API文件包含，用于基本验证和公共函数
 */

// 定义目录分隔符常量
if (!defined('DS')) {
    define('DS', DIRECTORY_SEPARATOR);
}

// 包含公共函数库
require_once (__DIR__) . DS . 'common.php';

// 检查是否已安装，如果未安装则会自动重定向或返回JSON响应
check_installed(true);

// 检查访问密码验证
check_access_password();

/**
 * 生成下载令牌
 * 
 * @param int $softwareId 软件ID
 * @param string $orderNo 订单号（可选）
 * @return string 下载令牌
 */
function generate_download_token($softwareId, $orderNo = '') {
    $timestamp = time();
    $secret = get_download_secret();
    $data = $softwareId . '|' . $timestamp . '|' . $orderNo;
    $signature = hash_hmac('sha256', $data, $secret);
    return base64_encode($data . '|' . $signature);
}

/**
 * 验证下载令牌
 * 
 * @param string $token 下载令牌
 * @param int $validMinutes 有效分钟数，默认为5分钟
 * @return array|false 成功返回包含软件ID和订单号的数组，失败返回false
 */
function validate_download_token($token, $validMinutes = 5) {
    try {
        $decoded = base64_decode($token);
        if ($decoded === false) {
            return false;
        }
        
        $parts = explode('|', $decoded);
        if (count($parts) < 4) {
            return false;
        }
        
        $softwareId = $parts[0];
        $timestamp = $parts[1];
        $orderNo = $parts[2];
        $signature = $parts[3];
        
        // 验证时间戳（令牌有效期为5分钟）
        $now = time();
        if ($now - $timestamp > $validMinutes * 60) {
            return false;
        }
        
        // 验证签名
        $secret = get_download_secret();
        $data = $softwareId . '|' . $timestamp . '|' . $orderNo;
        $expectedSignature = hash_hmac('sha256', $data, $secret);
        
        if (!hash_equals($expectedSignature, $signature)) {
            return false;
        }
        
        return [
            'software_id' => $softwareId,
            'order_no' => $orderNo
        ];
    } catch (Exception $e) {
        return false;
    }
}

/**
 * 获取下载密钥
 * 
 * @return string 下载密钥
 */
function get_download_secret() {
    // 从站点设置中获取密钥，如果不存在则使用安装时间作为密钥
    $settings = Settings::load();
    if (isset($settings['download_secret'])) {
        return $settings['download_secret'];
    }
    
    // 使用安装时间作为密钥
    if (isset($settings['installed_time'])) {
        return (string)$settings['installed_time'];
    }
    
    // 如果都不存在，使用一个默认值
    return 'nbbrj_default_download_secret';
}

/**
 * 记录下载日志
 * 
 * @param int $softwareId 软件ID
 * @param string $orderNo 订单号（可选）
 * @return bool 是否成功
 */
function log_download($softwareId, $orderNo = '') {
    $db = get_db_connection();
    
    // 获取客户端信息
    $ip = $_SERVER['REMOTE_ADDR'] ?? '';
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    $referer = $_SERVER['HTTP_REFERER'] ?? '';
    
    // 插入下载日志
    $stmt = $db->prepare('
        INSERT INTO download_logs (software_id, order_no, ip, user_agent, referer, created_at)
        VALUES (:software_id, :order_no, :ip, :user_agent, :referer, :created_at)
    ');
    
    $stmt->bindValue(':software_id', $softwareId, SQLITE3_INTEGER);
    $stmt->bindValue(':order_no', $orderNo, SQLITE3_TEXT);
    $stmt->bindValue(':ip', $ip, SQLITE3_TEXT);
    $stmt->bindValue(':user_agent', $userAgent, SQLITE3_TEXT);
    $stmt->bindValue(':referer', $referer, SQLITE3_TEXT);
    $stmt->bindValue(':created_at', time(), SQLITE3_INTEGER);
    
    $result = $stmt->execute();
    
    // 更新软件下载次数
    if ($result) {
        $db->exec("UPDATE softwares SET downloads = downloads + 1 WHERE id = $softwareId");
        return true;
    }
    
    return false;
}

/**
 * 检查访问密码验证
 *
 * @return void
 */
function check_access_password() {
    // 获取站点设置
    $settings = get_settings();
    $accessPassword = $settings['security']['access_password'] ?? '';

    // 如果没有设置访问密码，直接返回
    if (empty($accessPassword)) {
        return;
    }

    // 检查请求中的密码
    $inputPassword = get_password_from_request();

    if ($inputPassword && $inputPassword === $accessPassword) {
        // 密码正确，允许访问
        return;
    }

    // 检查请求头中的Accept字段
    $acceptHeader = $_SERVER['HTTP_ACCEPT'] ?? '';
    $wantsJson = strpos($acceptHeader, 'application/json') !== false;

    // 检查是否是API请求
    $currentScript = $_SERVER['SCRIPT_NAME'] ?? '';
    $isApiRequest = strpos($currentScript, '/api/') !== false;

    // 如果是API请求或客户端明确要求JSON响应
    if ($isApiRequest || $wantsJson) {
        // 返回JSON响应，要求验证
        header('HTTP/1.1 401 Unauthorized');
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'code' => 'access_password_required',
            'message' => '需要访问密码验证'
        ]);
        exit;
    } else {
        // 非API请求，直接重定向到验证页面
        header('Location: /security_verify.php');
        exit;
    }
}

/**
 * 从请求中获取访问密码
 *
 * @return string|null
 */
function get_password_from_request() {
    // 从X-Access-Password头获取
    $passwordHeader = $_SERVER['HTTP_X_ACCESS_PASSWORD'] ?? '';
    if (!empty($passwordHeader)) {
        return $passwordHeader;
    }

    // 从POST数据获取
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $input = json_decode(file_get_contents('php://input'), true);
        if (isset($input['access_password'])) {
            return $input['access_password'];
        }
    }

    // 从GET参数获取
    if (isset($_GET['access_password'])) {
        return $_GET['access_password'];
    }

    return null;
}


