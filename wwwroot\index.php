<?php
// 包含安装检查
$frontendConfig = require_once __DIR__ . '/common.php';

// 加载站点设置
$settings = get_settings();

// 设置默认值 - 从basic组中获取配置
$siteTitle = $settings['basic']['site_title'] ?? '娜宝贝软件';
$siteSubtitle = $settings['basic']['site_subtitle'] ?? '探索未来科技的软件集合';

// 获取分页配置、搜索配置和调试模式
$defaultPageSize = 10;
$maxPageSize = 100;
$showTotal = true;
$debugEnabled = false;
$searchMode = 'click';
$searchDelay = 500;

if (isset($settings['pagination'])) {
    if (isset($settings['pagination']['default_page_size'])) {
        $defaultPageSize = (int)$settings['pagination']['default_page_size'];
        if ($defaultPageSize < 1) {
            $defaultPageSize = 1;
        }
    }

    if (isset($settings['pagination']['max_page_size'])) {
        $maxPageSize = (int)$settings['pagination']['max_page_size'];
        if ($maxPageSize < 0) {
            $maxPageSize = 1;
        }
    }

    if (isset($settings['pagination']['show_total'])) {
        $showTotal = (bool)$settings['pagination']['show_total'];
    }
}

// 获取调试模式设置
if (isset($frontendConfig['debug'])) {
    $debugEnabled = (bool)$frontendConfig['debug'];
}

// 获取搜索配置
if (isset($settings['search'])) {
    if (isset($settings['search']['mode'])) {
        $searchMode = $settings['search']['mode'];
    }

    if (isset($settings['search']['delay'])) {
        $searchDelay = (int)$settings['search']['delay'];
    }
}

// 检查公告配置是否存在
$announcementEnabled = isset($settings['announcement']) && isset($settings['announcement']['enabled']) && $settings['announcement']['enabled'];
$announcementTitle = $settings['announcement']['title'] ?? '重要公告';
$announcementContent = $settings['announcement']['content'] ?? '欢迎来到娜宝贝软件官网！我们秉着良好的态度和服务，让我的客户优先享用最好用的下载服务，极速的下载体验，让您感受不一样的下载方式！';
$announcementStyle = $settings['announcement']['style'] ?? 'default';
$announcementRepeatShow = $settings['announcement']['repeat_show'] ?? false;
$announcementTimestamp = $settings['announcement']['timestamp'] ?? time();

// 根据样式设置颜色类
$styleClasses = [
    'default' => [
        'bg' => 'linear-gradient(135deg, #001133, #003366)',
        'border' => '#00aaff',
        'title' => '#00ffff'
    ],
    'info' => [
        'bg' => 'linear-gradient(135deg, #001a33, #003366)',
        'border' => '#0088ff',
        'title' => '#00ccff'
    ],
    'success' => [
        'bg' => 'linear-gradient(135deg, #003322, #005533)',
        'border' => '#00cc88',
        'title' => '#00ffaa'
    ],
    'warning' => [
        'bg' => 'linear-gradient(135deg, #332200, #553300)',
        'border' => '#ffaa00',
        'title' => '#ffcc00'
    ],
    'danger' => [
        'bg' => 'linear-gradient(135deg, #330000, #550000)',
        'border' => '#ff0000',
        'title' => '#ff5555'
    ]
];

// 获取当前样式
$currentStyle = $styleClasses[$announcementStyle] ?? $styleClasses['default'];

// 不再检查cookie，使用localStorage在前端处理
?>
<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <?php if (!empty($settings['basic']['site_favicon'])): ?>
    <link rel="icon" type="image/x-icon" href="<?php echo htmlspecialchars($settings['basic']['site_favicon']); ?>">
    <?php endif; ?>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title><?php echo htmlspecialchars($siteTitle); ?> - 高速下载</title>
    <script src="<?php echo $frontendConfig['assets_path']; ?>libs/tailwindcss/tailwind.js"></script>
    <link href="<?php echo $frontendConfig['assets_path']; ?>libs/font-awesome/css/all.min.css" rel="stylesheet">
    <script src="<?php echo $frontendConfig['assets_path']; ?>libs/md5/blueimp-md5.min.js"></script>
    <style>
        /* 全局样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
        }

        body {
            background-color: #0a0a1a;
            color: #e0e0ff;
            background-image:
                radial-gradient(circle at 10% 20%, rgba(0, 100, 255, 0.1) 0%, transparent 20%),
                radial-gradient(circle at 90% 80%, rgba(150, 0, 255, 0.1) 0%, transparent 20%);
            min-height: 100vh;
        }

        /* 头部样式 */
        header {
            background: linear-gradient(90deg, #001133, #003366);
            padding: 1rem;
            text-align: center;
            border-bottom: 2px solid #00aaff;
            box-shadow: 0 0 20px rgba(0, 170, 255, 0.3);
            position: relative;
            overflow: hidden;
        }

        header::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, transparent, #00ffff, transparent);
            animation: scanline 4s linear infinite;
        }

        @keyframes scanline {
            0% {
                top: 0;
            }

            100% {
                top: 100%;
            }
        }

        h1 {
            font-size: 1.8rem;
            color: #00ffff;
            text-shadow: 0 0 10px #00aaff, 0 0 20px #0066ff;
            letter-spacing: 1px;
            margin-bottom: 0.5rem;
        }

        .subtitle {
            color: #aaccff;
            font-size: 0.9rem;
            letter-spacing: 0.5px;
        }

        /* 主要内容区 */
        .container {
            display: flex;
            flex-direction: column;
            max-width: 1200px;
            margin: 1rem auto;
            padding: 0 1rem;
        }

        /* 左侧分类导航 */
        .sidebar {
            width: 100%;
            background: rgba(10, 20, 40, 0.7);
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1.5rem;
            border: 1px solid #334466;
            box-shadow: 0 0 15px rgba(0, 100, 255, 0.2);
            backdrop-filter: blur(5px);
        }

        .sidebar h2 {
            color: #00ccff;
            font-size: 1.2rem;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #334477;
            text-shadow: 0 0 5px #00aaff;
        }

        .category-list {
            list-style: none;
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
            gap: 0.5rem;
        }

        .category-item {
            margin-bottom: 0;
            position: relative;
            transition: all 0.3s ease;
        }

        .category-item::before {
            content: "▶";
            color: #00aaff;
            position: absolute;
            left: -1rem;
            opacity: 0;
            transition: all 0.3s ease;
        }

        .category-item:hover::before {
            opacity: 1;
            left: -0.5rem;
        }

        .category-item a {
            color: #aaccff;
            text-decoration: none;
            display: block;
            padding: 0.5rem;
            border-radius: 4px;
            transition: all 0.3s ease;
            position: relative;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.9rem;
        }

        .category-item a:hover {
            background: rgba(0, 100, 255, 0.2);
            color: #00ffff;
            transform: translateX(5px);
        }

        .category-item.active a {
            background: rgba(0, 100, 255, 0.3);
            color: #00ffff;
            border-left: 3px solid #00aaff;
        }

        .category-badge {
            background: rgba(0, 170, 255, 0.3);
            color: #00ffff;
            padding: 0.1rem 0.3rem;
            border-radius: 999px;
            font-size: 0.7rem;
        }

        /* 右侧内容区 */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        /* 顶部搜索框 */
        .top-search {
            margin-bottom: 1rem;
            position: relative;
            width: 100%;
            height: 50px;
            background: rgba(10, 20, 40, 0.7);
            border-radius: 8px;
            border: 1px solid #334466;
            box-shadow: 0 0 15px rgba(0, 100, 255, 0.2);
            padding: 0.5rem;
            overflow: hidden;
        }

        .top-search::before {
            content: "";
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(0, 170, 255, 0.2), transparent);
            animation: search-glow 3s infinite;
        }

        @keyframes search-glow {
            0% {
                left: -100%;
            }

            100% {
                left: 100%;
            }
        }

        .top-search input {
            width: 100%;
            height: 100%;
            padding: 0 1rem;
            background: transparent;
            border: none;
            color: #e0e0ff;
            outline: none;
            font-size: 1rem;
        }

        /* 即时搜索模式下的输入框样式 */
        .instant-search input {
            padding-right: 1rem;
        }

        /* 点击搜索模式下的输入框样式 */
        .click-search input {
            padding-right: 6rem;
        }

        .top-search button {
            position: absolute;
            right: 0.5rem;
            top: 50%;
            transform: translateY(-50%);
            background: linear-gradient(135deg, #00aaff, #0066ff);
            color: #ffffff;
            border: none;
            border-radius: 4px;
            padding: 0.4rem 0.8rem;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 0 5px rgba(0, 170, 255, 0.3);
            font-size: 0.9rem;
        }

        .top-search button:hover {
            background: linear-gradient(135deg, #00ccff, #0088ff);
            box-shadow: 0 0 10px rgba(0, 170, 255, 0.5);
        }

        /* 软件列表 */
        .software-list {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .software-card {
            background: rgba(20, 30, 60, 0.6);
            border-radius: 8px;
            padding: 1rem;
            border: 1px solid #445588;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            opacity: 0;
            transform: translateY(20px);
            animation: fadeIn 0.5s forwards;
        }

        @keyframes fadeIn {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .software-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 100, 255, 0.2);
            border-color: #00aaff;
        }

        .software-card::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(0, 170, 255, 0.1) 0%, transparent 50%);
            z-index: -1;
        }

        /* 分类徽章 */
        .software-badge {
            display: inline-block;
            padding: 0.2rem 0.5rem;
            border-radius: 4px;
            font-size: 0.8rem;
            margin-bottom: 0.5rem;
        }

        .badge-dev {
            background: rgba(0, 170, 255, 0.2);
            color: #00aaff;
        }

        .badge-design {
            background: rgba(170, 0, 255, 0.2);
            color: #aa00ff;
        }

        .badge-security {
            background: rgba(0, 255, 170, 0.2);
            color: #00ffaa;
        }

        .badge-browser {
            background: rgba(255, 170, 0, 0.2);
            color: #ffaa00;
        }

        .badge-system {
            background: rgba(255, 0, 170, 0.2);
            color: #ff00aa;
        }

        /* 图标区域 */
        .software-icon-container {
            width: 60px;
            height: 60px;
            margin-right: 1rem;
            margin-bottom: 0.5rem;
            flex-shrink: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(0, 100, 255, 0.1);
            border-radius: 8px;
            border: 1px solid #334477;
            position: relative;
            overflow: hidden;
        }

        .software-icon {
            width: 40px;
            height: 40px;
            object-fit: contain;
        }

        /* 内容区域 */
        .software-content {
            flex: 1;
        }

        .software-content h3 {
            color: #00ccff;
            font-size: 1.1rem;
            margin-bottom: 0.5rem;
        }

        .software-description {
            color: #aaccff;
            font-size: 0.85rem;
            line-height: 1.4;
            margin-bottom: 0.8rem;
        }

        .software-meta {
            display: flex;
            flex-wrap: wrap;
            color: #7788aa;
            font-size: 0.8rem;
            gap: 0.8rem;
            margin-bottom: 0.8rem;
        }

        /* 下载按钮区域 */
        .software-actions {
            width: 100%;
            flex-shrink: 0;
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
            gap: 0.5rem;
        }

        .download-btn, .video-demo-btn, .baidu-disk-btn, .purchase-btn {
            display: inline-block;
            color: #ffffff;
            padding: 0.6rem 0.5rem;
            border-radius: 4px;
            text-decoration: none;
            font-weight: bold;
            text-align: center;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            position: relative;
            overflow: hidden;
            font-size: 0.85rem;
        }

        .download-btn {
            background: linear-gradient(135deg, #00aaff, #0066ff);
        }

        .download-btn:hover {
            background: linear-gradient(135deg, #00ccff, #0088ff);
            box-shadow: 0 0 10px rgba(0, 170, 255, 0.5);
        }

        .download-btn::after {
            content: "↓";
            position: absolute;
            right: 0.5rem;
            opacity: 0;
            transition: all 0.3s ease;
        }

        .download-btn:hover::after {
            opacity: 1;
            right: 0.3rem;
        }

        /* 视频演示按钮 */
        .video-demo-btn {
            background: linear-gradient(135deg, #00aaff, #0066ff);
        }

        .video-demo-btn:hover {
            background: linear-gradient(135deg, #00ccff, #0088ff);
            box-shadow: 0 0 10px rgba(0, 170, 255, 0.5);
        }

        .video-demo-btn::after {
            content: "▶";
            position: absolute;
            right: 0.5rem;
            opacity: 0;
            transition: all 0.3s ease;
        }

        .video-demo-btn:hover::after {
            opacity: 1;
            right: 0.3rem;
        }

        /* 百度网盘下载按钮 */
        .baidu-disk-btn {
            background: linear-gradient(135deg, #00aa77, #006644);
        }

        .baidu-disk-btn:hover {
            background: linear-gradient(135deg, #00cc99, #008866);
            box-shadow: 0 0 10px rgba(0, 170, 120, 0.5);
        }

        .baidu-disk-btn::after {
            content: "云";
            position: absolute;
            right: 0.5rem;
            opacity: 0;
            transition: all 0.3s ease;
        }

        .baidu-disk-btn:hover::after {
            opacity: 1;
            right: 0.3rem;
        }

        /* 自助购买按钮 */
        .purchase-btn {
            background: linear-gradient(135deg, #ff5500, #aa3300);
        }

        .purchase-btn:hover {
            background: linear-gradient(135deg, #ff7722, #cc5500);
            box-shadow: 0 0 10px rgba(255, 100, 0, 0.5);
        }

        .purchase-btn::after {
            content: "购";
            position: absolute;
            right: 0.5rem;
            opacity: 0;
            transition: all 0.3s ease;
        }

        .purchase-btn:hover::after {
            opacity: 1;
            right: 0.3rem;
        }

        /* 页脚 */
        footer {
            text-align: center;
            padding: 1.5rem;
            color: #7788aa;
            font-size: 0.8rem;
            border-top: 1px solid #223344;
            margin-top: 1.5rem;
        }

        /* 修改后的关于我们按钮样式 */
        .about-btn {
            position: absolute;
            right: 0.5rem;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(0, 170, 255, 0.2);
            color: #00ccff;
            border: 1px solid #00aaff;
            border-radius: 4px;
            padding: 0.4rem 0.8rem;
            cursor: pointer;
            transition: all 0.3s ease;
            z-index: 10;
            box-shadow: 0 0 10px rgba(0, 170, 255, 0.3);
            font-size: 0.8rem;
        }

        .about-btn:hover {
            background: rgba(0, 170, 255, 0.4);
            box-shadow: 0 0 15px rgba(0, 170, 255, 0.5);
        }

        /* 二维码弹窗样式 */
        .qr-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 30, 0.8);
            backdrop-filter: blur(5px);
            z-index: 100;
            justify-content: center;
            align-items: center;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .qr-modal.active {
            display: flex;
            opacity: 1;
        }

        .qr-content {
            background: linear-gradient(135deg, #001133, #003366);
            padding: 1.5rem;
            border-radius: 10px;
            border: 1px solid #00aaff;
            box-shadow: 0 0 30px rgba(0, 170, 255, 0.5);
            text-align: center;
            position: relative;
            max-width: 90%;
            width: 300px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                box-shadow: 0 0 30px rgba(0, 170, 255, 0.5);
            }

            50% {
                box-shadow: 0 0 50px rgba(0, 170, 255, 0.8);
            }

            100% {
                box-shadow: 0 0 30px rgba(0, 170, 255, 0.5);
            }
        }

        /* 支付弹窗专用的发光动画 */
        @keyframes paymentPulse {
            0%, 100% {
                box-shadow:
                    0 10px 15px -3px rgba(0, 0, 0, 0.1),
                    0 4px 6px -2px rgba(0, 0, 0, 0.05),
                    0 0 30px rgba(0, 170, 255, 0.3),
                    0 0 60px rgba(0, 170, 255, 0.1);
                border-color: rgba(0, 170, 255, 0.3);
            }
            50% {
                box-shadow:
                    0 10px 15px -3px rgba(0, 0, 0, 0.1),
                    0 4px 6px -2px rgba(0, 0, 0, 0.05),
                    0 0 40px rgba(0, 170, 255, 0.5),
                    0 0 80px rgba(0, 170, 255, 0.2);
                border-color: rgba(0, 170, 255, 0.5);
            }
        }

        /* 支付宝按钮加载动画 */
        @keyframes alipayPulse {
            0%, 100% {
                background-color: #2563eb;
                transform: scale(1);
            }
            50% {
                background-color: #1d4ed8;
                transform: scale(1.02);
            }
        }

        .qr-content h3 {
            color: #00ffff;
            margin-bottom: 1rem;
            text-shadow: 0 0 10px #00aaff;
            font-size: 1.2rem;
        }

        .qr-code {
            width: 180px;
            height: 180px;
            margin: 0 auto 1rem;
            background: white;
            padding: 10px;
            border-radius: 5px;
            position: relative;
        }

        .qr-code::before {
            content: "";
            position: absolute;
            top: -5px;
            left: -5px;
            right: -5px;
            bottom: -5px;
            border: 2px solid #00ffff;
            border-radius: 8px;
            animation: borderGlow 2s infinite;
            z-index: -1;
        }

        @keyframes borderGlow {
            0% {
                box-shadow: 0 0 10px #00aaff;
            }

            50% {
                box-shadow: 0 0 20px #00ffff;
            }

            100% {
                box-shadow: 0 0 10px #00aaff;
            }
        }

        .qr-close {
            position: absolute;
            top: 10px;
            right: 10px;
            color: #00ffff;
            font-size: 1.2rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .qr-close:hover {
            transform: rotate(90deg);
            color: #ff0000;
        }

        .qr-desc {
            color: #aaccff;
            font-size: 0.8rem;
            margin-top: 1rem;
        }

        /* 进入弹窗公告样式 */
        .announcement-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 30, 0.8);
            backdrop-filter: blur(5px);
            z-index: 200;
            justify-content: center;
            align-items: center;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .announcement-modal.active {
            display: flex;
            opacity: 1;
        }

        .announcement-content {
            background: linear-gradient(135deg, #001133, #003366);
            padding: 1.5rem;
            border-radius: 10px;
            border: 1px solid #00aaff;
            box-shadow: 0 0 30px rgba(0, 170, 255, 0.5);
            text-align: center;
            position: relative;
            max-width: 90%;
            width: 500px;
            animation: pulse 2s infinite;
        }

        .announcement-close {
            position: absolute;
            top: 10px;
            right: 10px;
            color: #00ffff;
            font-size: 1.2rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .announcement-close:hover {
            transform: rotate(90deg);
            color: #ff0000;
        }

        .announcement-title {
            color: #00ffff;
            font-size: 1.3rem;
            margin-bottom: 1rem;
            text-shadow: 0 0 10px #00aaff;
        }

        .announcement-text {
            color: #aaccff;
            font-size: 0.9rem;
            line-height: 1.4;
            margin-bottom: 1.2rem;
        }

        .announcement-btn {
            display: inline-block;
            background: linear-gradient(135deg, #00aaff, #0066ff);
            color: #ffffff;
            padding: 0.6rem 1.2rem;
            border-radius: 4px;
            text-decoration: none;
            font-weight: bold;
            text-align: center;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            font-size: 0.9rem;
        }

        .announcement-btn:hover {
            background: linear-gradient(135deg, #00ccff, #0088ff);
            box-shadow: 0 0 10px rgba(0, 170, 255, 0.5);
        }

        /* 搜索结果为空提示 */
        .search-empty {
            text-align: center;
            padding: 2rem 0;
            opacity: 0;
            transform: translateY(20px);
            animation: fadeIn 0.5s forwards;
        }

        .search-empty h3 {
            color: #00ccff;
            font-size: 1.2rem;
            margin-bottom: 0.8rem;
        }

        .search-empty p {
            color: #aaccff;
            font-size: 0.9rem;
        }

        /* 下载选项弹窗样式 */
        .download-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 30, 0.8);
            backdrop-filter: blur(5px);
            z-index: 150;
            justify-content: center;
            align-items: center;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .download-modal.active {
            display: flex;
            opacity: 1;
        }

        .download-content {
            background: linear-gradient(135deg, #001133, #003366);
            padding: 1.5rem;
            border-radius: 10px;
            border: 1px solid #00aaff;
            box-shadow: 0 0 30px rgba(0, 170, 255, 0.5);
            text-align: center;
            position: relative;
            max-width: 90%;
            width: 500px;
            animation: pulse 2s infinite;
        }

        .download-title {
            color: #00ffff;
            font-size: 1.3rem;
            margin-bottom: 1rem;
            text-shadow: 0 0 10px #00aaff;
        }

        .download-note {
            color: #aaccff;
            font-size: 0.9rem;
            margin: 1rem 0;
            padding: 0.8rem;
            background: rgba(0, 50, 100, 0.3);
            border-radius: 6px;
            border-left: 3px solid #00aaff;
            text-align: left;
        }

        .download-options {
            display: flex;
            flex-direction: column;
            gap: 0.8rem;
            margin-bottom: 1.5rem;
        }

        .download-option {
            display: flex;
            align-items: center;
            padding: 0.8rem;
            background: rgba(0, 100, 255, 0.2);
            border-radius: 6px;
            border: 1px solid #334477;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .download-option:hover {
            background: rgba(0, 170, 255, 0.3);
            border-color: #00aaff;
        }

        .download-option-icon {
            width: 30px;
            height: 30px;
            margin-right: 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(0, 170, 255, 0.2);
            border-radius: 50%;
            color: #00aaff;
        }

        .download-option-text {
            flex: 1;
            text-align: left;
        }

        .download-option-name {
            color: #00ccff;
            font-weight: bold;
            margin-bottom: 0.2rem;
        }

        .download-option-desc {
            color: #aaccff;
            font-size: 0.8rem;
        }

        .download-option-buttons {
            display: flex;
            gap: 0.5rem;
        }

        .download-copy-btn, .download-now-btn {
            background: rgba(0, 170, 255, 0.3);
            color: #00ffff;
            border: none;
            border-radius: 4px;
            padding: 0.3rem 0.6rem;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.8rem;
        }

        .download-now-btn {
            background: rgba(0, 200, 100, 0.3);
        }

        .download-now-btn:hover {
            background: rgba(0, 200, 100, 0.5);
        }

        .download-copy-btn:hover {
            background: rgba(0, 170, 255, 0.5);
        }

        /* 平板和桌面布局 */
        @media (min-width: 768px) {
            header {
                padding: 1.5rem;
            }

            h1 {
                font-size: 2rem;
            }

            .container {
                flex-direction: row;
                margin: 2rem auto;
            }

            .sidebar {
                width: 250px;
                margin-right: 1.5rem;
                margin-bottom: 0;
            }

            .category-list {
                display: block;
                grid-template-columns: none;
            }

            .software-card {
                flex-direction: row;
                padding: 1.5rem;
                align-items: center;
            }

            .software-icon-container {
                width: 80px;
                height: 80px;
                margin-bottom: 0;
            }

            .software-icon {
                width: 50px;
                height: 50px;
            }

            .software-content h3 {
                font-size: 1.2rem;
            }

            .software-description {
                font-size: 0.9rem;
            }

            .software-actions {
                width: 150px;
                display: flex;
                flex-direction: column;
                grid-template-columns: none;
            }

            .download-btn, .video-demo-btn, .baidu-disk-btn, .purchase-btn {
                padding: 0.6rem 1rem;
                font-size: 0.85rem;
            }
        }

        /* 大屏幕布局 */
        @media (min-width: 992px) {
            h1 {
                font-size: 2.5rem;
            }

            .subtitle {
                font-size: 1rem;
            }

            .software-content h3 {
                font-size: 1.3rem;
            }

            .software-description {
                font-size: 0.95rem;
            }
        }
    </style>
    <script src="<?php echo $frontendConfig['assets_path']; ?>libs/vue/vue.min.js"></script>
    <script type="text/x-template" id="category-tree-template">
        <div class="category-tree">
            <li v-for="category in categories" :key="category.id"
                class="category-item"
                :class="{ 'active': activeCategory === category.name }"
                :data-category="category.name">
                <a href="#" @click.prevent="handleCategoryClick(category)">
                    <!-- 缩进和图标 -->
                    <span v-if="category.level > 0" class="category-indent" :style="{ marginLeft: (category.level * 16) + 'px' }"></span>
                    <i v-if="category.children && category.children.length > 0"
                       class="fas fa-caret-right category-toggle"
                       :class="{ 'rotate-90': isExpanded(category.id) }"></i>
                    <i v-else-if="category.level > 0" class="fas fa-minus category-bullet"></i>

                    <!-- 分类名称和数量 -->
                    <span>{{ category.name }}</span>
                    <span class="category-badge">{{ category.total_software_count || category.software_count || 0 }}个</span>
                </a>

                <!-- 递归显示子分类 -->
                <div v-if="category.children && category.children.length > 0"
                     class="subcategory-container"
                     :class="{ 'hidden': !isExpanded(category.id) }">
                    <category-tree
                        :categories="category.children"
                        :active-category="activeCategory"
                        :expanded-categories="expandedCategories"
                        @select-category="$emit('select-category', $event)"
                        @toggle-expand="toggleExpand">
                    </category-tree>
                </div>
            </li>
        </div>
    </script>
    <style>
        /* 树状分类样式 */
        .category-toggle {
            transition: transform 0.2s;
            margin-right: 5px;
        }
        .rotate-90 {
            transform: rotate(90deg);
        }
        .category-bullet {
            font-size: 8px;
            margin-right: 5px;
            vertical-align: middle;
        }
        .subcategory-container {
            margin-left: 10px;
        }
        .hidden {
            display: none;
        }

        /* 分页样式 */
        .pagination-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 2rem;
            padding: 1rem;
            background: rgba(10, 20, 40, 0.7);
            border-radius: 8px;
            border: 1px solid #334466;
        }

        .pagination-info {
            color: #aaccff;
            font-size: 0.9rem;
        }

        .pagination-controls {
            display: flex;
            gap: 0.5rem;
        }

        .pagination-btn {
            background: linear-gradient(135deg, #00aaff, #0066ff);
            color: #ffffff;
            border: none;
            border-radius: 4px;
            padding: 0.5rem 0.8rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .pagination-btn:hover:not(.disabled) {
            background: linear-gradient(135deg, #00ccff, #0088ff);
            box-shadow: 0 0 10px rgba(0, 170, 255, 0.5);
        }

        .pagination-btn.disabled {
            opacity: 0.5;
            cursor: not-allowed;
            background: linear-gradient(135deg, #336699, #003366);
        }

        /* 访问密码验证弹窗样式 */
        #access-password-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.8);
            z-index: 10000;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .access-password-modal {
            width: 400px;
            max-width: 90%;
            background-color: rgba(16, 42, 87, 0.95);
            border: 1px solid rgba(100, 200, 255, 0.3);
            border-radius: 8px;
            box-shadow: 0 0 20px rgba(100, 200, 255, 0.4),
                        inset 0 0 15px rgba(100, 200, 255, 0.2);
            overflow: hidden;
        }

        .access-password-header {
            padding: 1.5rem;
            text-align: center;
            border-bottom: 1px solid rgba(100, 200, 255, 0.2);
        }

        .access-password-header h2 {
            margin: 0;
            color: #e6f1ff;
            font-size: 1.5rem;
            text-shadow: 0 0 10px rgba(100, 200, 255, 0.6);
        }

        .access-password-body {
            padding: 2rem;
        }

        .password-group {
            margin-bottom: 1.5rem;
        }

        .password-label {
            display: block;
            margin-bottom: 0.5rem;
            color: #a8b2d1;
            font-size: 0.9rem;
        }

        .password-input {
            width: 100%;
            padding: 0.75rem;
            background-color: rgba(10, 25, 47, 0.8);
            border: 1px solid rgba(100, 200, 255, 0.3);
            color: #e6f1ff;
            border-radius: 4px;
            font-size: 1rem;
            outline: none;
            box-sizing: border-box;
            transition: all 0.3s;
        }

        .password-input:focus {
            border-color: #64ffda;
            box-shadow: 0 0 8px rgba(100, 255, 218, 0.5);
        }

        .submit-btn {
            width: 100%;
            padding: 0.75rem;
            background-color: #1e90ff;
            border: none;
            color: white;
            font-weight: bold;
            border-radius: 4px;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s;
            margin-bottom: 1rem;
        }

        .submit-btn:hover {
            background-color: #0077ff;
            box-shadow: 0 0 10px rgba(30, 144, 255, 0.7);
        }

        .submit-btn:disabled {
            background-color: #666;
            cursor: not-allowed;
        }

        .status-text {
            text-align: center;
            font-size: 0.9rem;
            min-height: 1.2rem;
        }

        .status-text.loading {
            color: #64ffda;
            animation: fadePulse 1.5s infinite;
        }

        .status-text.success {
            color: #64ffda;
        }

        .status-text.error {
            color: #ff5555;
        }

        @keyframes fadePulse {
            0%, 100% { opacity: 0.6; }
            50% { opacity: 1; }
        }

        @media (max-width: 768px) {
            .access-password-modal {
                width: 350px;
            }

            .access-password-body {
                padding: 1.5rem;
            }
        }
    </style>
</head>

<body>
    <!-- 设备检测和跳转脚本 -->
    <script src="<?php echo $frontendConfig['assets_path']; ?>js/device-detection.js"></script>
    <script>
        // 设备跳转配置（从后台设置加载）
        <?php
        $settings = get_settings();
        $deviceRedirectConfig = isset($settings['device_redirect']) ? $settings['device_redirect'] : ['enabled' => false];
        ?>
        window.deviceRedirectConfig = <?php echo json_encode($deviceRedirectConfig); ?>;

        // 初始化设备跳转管理器
        function initDeviceRedirect() {
            try {
                // 确保设备检测模块已加载
                if (typeof window.deviceRedirectManager === 'undefined' || !window.deviceDetectionLoaded) {
                    // 如果模块未加载，添加到等待队列
                    if (!window.deviceDetectionCallbacks) {
                        window.deviceDetectionCallbacks = [];
                    }
                    window.deviceDetectionCallbacks.push(initDeviceRedirect);
                    return;
                }

                // 设置跳转配置
                window.deviceRedirectManager.setRedirectConfig(window.deviceRedirectConfig);

                // 在调试模式下启用详细日志
                <?php if (isset($frontendConfig['debug']) && $frontendConfig['debug']): ?>
                window.deviceRedirectManager.setDebugMode(true);
                console.log('[设备跳转] 当前设备信息:', window.deviceRedirectManager.getDeviceInfo());
                <?php endif; ?>

                // 执行设备检测和跳转
                window.deviceRedirectManager.executeRedirect();
            } catch (error) {
                console.error('[设备跳转] 初始化失败:', error);
            }
        }

        // 立即尝试初始化（如果模块已加载）
        initDeviceRedirect();

        // 页面加载完成后也尝试初始化（防止遗漏）
        document.addEventListener('DOMContentLoaded', function() {
            // 延迟一点时间确保所有脚本都已加载
            setTimeout(initDeviceRedirect, 50);
        });
    </script>

    <!-- 隐藏的音频播放器 -->
    <audio id="bg-music" loop="false" autoplay="true" style="display: none;">
        <source src="https://interstellar-software.s3.amazonaws.com/ambient-space-music.mp3" type="audio/mpeg">
    </audio>

    <!-- 进入弹窗公告 -->
    <div class="announcement-modal" id="announcementModal" style="<?php echo !$announcementEnabled ? 'display:none;' : ''; ?>">
        <div class="announcement-content" style="background: <?php echo $currentStyle['bg']; ?>; border-color: <?php echo $currentStyle['border']; ?>">
            <span class="announcement-close" id="announcementClose">&times;</span>
            <h3 class="announcement-title" style="color: <?php echo $currentStyle['title']; ?>"><?php echo htmlspecialchars($announcementTitle); ?></h3>
            <div class="announcement-text"><?php echo $announcementContent; ?></div>
            <button class="announcement-btn" id="announcementConfirm">我知道了</button>
        </div>
    </div>

    <div id="app">
        <header>
            <!-- 新增的关于我们按钮 -->
            <button class="about-btn" id="aboutBtn">
                <i class="fas fa-info-circle"></i> 关于我们
            </button>

            <h1><?php echo htmlspecialchars($siteTitle); ?></h1>
            <p class="subtitle"><?php echo htmlspecialchars($siteSubtitle); ?></p>
        </header>

        <!-- 二维码弹窗 -->
        <div class="qr-modal" id="qrModal">
            <div class="qr-content">
                <span class="qr-close" id="qrClose">&times;</span>
                <h3>扫描二维码关注我们</h3>
                <div class="qr-code">
                    <!-- 使用配置中的二维码图片 -->
                    <img src="<?php echo isset($settings['basic']['qr_code_url']) ? htmlspecialchars($settings['basic']['qr_code_url']) : 'https://via.placeholder.com/200'; ?>" alt="二维码" style="width:100%;height:100%;">
                </div>
                <p class="qr-desc">扫描上方二维码，获取更多软件资源和更新信息</p>
            </div>
        </div>

    <!-- 通用弹窗容器 -->
    <div class="universal-modal" id="universalModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0, 0, 30, 0.8); backdrop-filter: blur(5px); z-index: 150; justify-content: center; align-items: center; opacity: 0; transition: opacity 0.3s ease;">
        <div class="universal-content" id="universalContent" style="background: linear-gradient(135deg, #001133, #003366); padding: 1.5rem; border-radius: 10px; border: 1px solid #00aaff; box-shadow: 0 0 30px rgba(0, 170, 255, 0.5); text-align: center; position: relative; max-width: 90%; width: 500px; animation: pulse 2s infinite;">
            <!-- 内容将在这里动态替换 -->
        </div>
    </div>

        <div class="container">
            <!-- 左侧分类导航 -->
            <aside class="sidebar">
                <h2>软件分类</h2>
                <ul class="category-list">
                    <li class="category-item active" data-category="all" @click.prevent="setCategory('all')">
                        <a href="#">全部软件 <span class="category-badge">{{ totalSoftwareCount }}个</span></a>
                    </li>
                    <!-- 递归组件用于显示树状分类 -->
                    <category-tree
                        :categories="categories"
                        :active-category="activeCategory"
                        :expanded-categories="expandedCategories"
                        @select-category="setCategory"
                        @toggle-expand="toggleCategoryExpand">
                    </category-tree>
                </ul>
            </aside>

            <!-- 右侧内容区 -->
            <div class="main-content">
                <!-- 顶部搜索框 -->
                <div class="top-search">
                    <input type="text" placeholder="搜索软件名称、..."
                           v-model="searchTerm"
                           @keyup.enter="search"
                           @input="onSearchInput">
                    <button v-if="searchConfig.mode !== 'instant'" @click="search"><i class="fa fa-search"></i> 搜索</button>
                </div>

                <main class="software-list">
                    <!-- 软件卡片 -->
                    <div v-for="software in filteredSoftware" :key="software.id" class="software-card" :data-category="getCategoryName(software.category)">
                        <div class="software-icon-container">
                            <img :src="software.icon || 'https://via.placeholder.com/50'" :alt="software.name" class="software-icon">
                        </div>
                        <div class="software-content">
                            <span class="software-badge badge-dev">{{ getCategoryName(software.category) }}</span>
                            <h3>{{ software.name }}</h3>
                            <p class="software-description">{{ software.description }}</p>
                            <div class="software-meta">
                                <span v-if="software.version">版本: {{ software.version }}</span>
                                <span v-if="software.size">大小: {{ software.size }}</span>
                                <span>下载量: {{ software.display_downloads }}</span>
                                <span v-if="software.price">价格: {{ software.price }}</span>
                            </div>
                        </div>
                        <div class="software-actions">
                            <!-- 价格为0或已购买显示下载选项，否则显示自助购买 -->
                            <button v-if="(software.price_value === 0 || software.has_paid)"
                                    class="download-btn"
                                    @click="showDownloadOptions(software)"
                                    :data-software-id="software.id"
                                    :data-software-name="software.name">下载选项</button>
                            <button v-if="software.price_value > 0 && !software.has_paid"
                                    class="purchase-btn"
                                    @click="handlePurchase(software)"
                                    :data-software-id="software.id"
                                    :data-software-name="software.name">自助购买</button>
                            <a v-if="software.video_url" :href="software.video_url" class="video-demo-btn" target="_blank">视频演示</a>
                        </div>
                    </div>

                    <!-- 搜索结果为空提示 -->
                    <div class="search-empty" v-if="filteredSoftware.length === 0">
                        <h3>未找到匹配的软件</h3>
                        <p>请尝试使用不同的关键词或检查拼写</p>
                    </div>

                    <!-- 分页控件 -->
                    <div class="pagination-container" v-if="pagination.total > 0 && pagination.pages > 1">
                        <div class="pagination-info" v-if="paginationConfig.show_total">
                            共 {{ pagination.total }} 条记录，第 {{ pagination.page }}/{{ pagination.pages }} 页
                        </div>
                        <div class="pagination-controls">
                            <button @click="changePage(1)" :disabled="pagination.page === 1" class="pagination-btn" :class="{ 'disabled': pagination.page === 1 }">
                                <i class="fas fa-angle-double-left"></i>
                            </button>
                            <button @click="changePage(pagination.page - 1)" :disabled="pagination.page === 1" class="pagination-btn" :class="{ 'disabled': pagination.page === 1 }">
                                <i class="fas fa-angle-left"></i>
                            </button>
                            <button @click="changePage(pagination.page + 1)" :disabled="pagination.page === pagination.pages" class="pagination-btn" :class="{ 'disabled': pagination.page === pagination.pages }">
                                <i class="fas fa-angle-right"></i>
                            </button>
                            <button @click="changePage(pagination.pages)" :disabled="pagination.page === pagination.pages" class="pagination-btn" :class="{ 'disabled': pagination.page === pagination.pages }">
                                <i class="fas fa-angle-double-right"></i>
                            </button>
                        </div>
                    </div>

                    <?php if (isset($frontendConfig['debug']) && $frontendConfig['debug']): ?>
                    <!-- 调试信息 -->
                    <div v-if="debugEnabled" class="debug-info" style="margin-top: 20px; padding: 10px; background: rgba(0,0,0,0.8); color: #00ff00; font-family: monospace; font-size: 14px; border-radius: 5px; border: 2px solid #00ff00;">
                        <h3 style="color: #ff0000; margin-top: 0;">调试信息</h3>
                        <div><strong>分页配置:</strong> {{ JSON.stringify(paginationConfig) }}</div>
                        <div><strong>分页状态:</strong> {{ JSON.stringify(pagination) }}</div>
                        <div><strong>显示条件:</strong> pagination.total({{ pagination.total }}) > 0 && pagination.pages({{ pagination.pages }}) > 1 = {{ (pagination.total > 0 && pagination.pages > 1) ? '满足' : '不满足' }}</div>
                        <div><strong>软件数量:</strong> {{ filteredSoftware.length }}</div>
                        <div><strong>总页数:</strong> {{ pagination.pages }}</div>
                        <div><strong>当前页:</strong> {{ pagination.page }}</div>
                        <div><strong>调试模式:</strong> {{ debugEnabled ? '开启' : '关闭' }}</div>

                        <div style="margin-top: 10px; display: flex; gap: 10px;">
                            <button @click="changePage(1)" style="background: #00aa00; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer;">第一页</button>
                            <button @click="changePage(pagination.page - 1)" :disabled="pagination.page <= 1" style="background: #00aa00; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer;">上一页</button>
                            <button @click="changePage(pagination.page + 1)" :disabled="pagination.page >= pagination.pages" style="background: #00aa00; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer;">下一页</button>
                            <button @click="changePage(pagination.pages)" style="background: #00aa00; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer;">最后一页</button>
                        </div>

                        <div style="margin-top: 10px;">
                            <button @click="reloadConfig" style="background: #aa0000; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer;">重新加载配置</button>
                            <button @click="reloadData" style="background: #aa0000; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer; margin-left: 10px;">重新加载数据</button>
                        </div>
                    </div>
                    <?php endif; ?>
                </main>
            </div>
        </div>

        <footer>
            <p>© <?php echo date('Y'); ?> <?php echo htmlspecialchars($siteTitle); ?> | <?php echo htmlspecialchars($siteSubtitle); ?></p>
        </footer>
    </div>

    <!-- 引入API工具函数和软件处理函数 -->
    <script src="<?php echo $frontendConfig['assets_path']; ?>js/api.js"></script>

    <!-- 支付配置 -->
    <script>
        // 直接从PHP传递支付配置
        const paymentConfig = {
            enabled: <?php
                $paymentConfig = get_payment_config();
                echo ($paymentConfig && $paymentConfig['enabled']) ? 'true' : 'false';
            ?>,
            available_payments: [
                <?php
                    if ($paymentConfig && $paymentConfig['enabled']) {
                        $payments = [];
                        if (isset($paymentConfig['wechat_pay']) && $paymentConfig['wechat_pay']['enabled']) {
                            $payments[] = '"wechat_pay"';
                        }
                        if (isset($paymentConfig['alipay']) && $paymentConfig['alipay']['enabled']) {
                            $payments[] = '"alipay"';
                        }
                        echo implode(',', $payments);
                    }
                ?>
            ],
            button_layout: '<?php
                $settings = Settings::getAll();
                echo isset($settings['payment']['button_layout']) ? $settings['payment']['button_layout'] : 'vertical';
            ?>',
            is_mobile: <?php echo is_mobile_device() ? 'true' : 'false'; ?>
        };

        // 站点配置
        window.siteConfig = {
            frontendUrl: '<?php echo addslashes($settings['basic']['frontend_url'] ?? ''); ?>'
        };

        // 下载选项配置
        window.downloadConfig = {
            <?php
                $settings = Settings::getAll();
                // 检查是否有下载配置
                if (isset($settings['download'])) {
                    $downloadSettings = $settings['download'];
                    for ($i = 1; $i <= 5; $i++) {
                        if (isset($downloadSettings["url_{$i}"])) {
                            $config = $downloadSettings["url_{$i}"];
                            echo "$i: {\n";
                            if (isset($config['name'])) {
                                echo "                name: '" . addslashes($config['name']) . "',\n";
                            }
                            if (isset($config['icon'])) {
                                echo "                icon: '" . addslashes($config['icon']) . "',\n";
                            }
                            if (isset($config['description'])) {
                                echo "                description: '" . addslashes($config['description']) . "',\n";
                            }
                            if (isset($config['useDownloadUrl'])) {
                                echo "                useDownloadUrl: " . ($config['useDownloadUrl'] ? 'true' : 'false') . "\n";
                            }
                            echo "            },\n";
                        }
                    }
                }
            ?>
        };
    </script>

    <script src="<?php echo $frontendConfig['assets_path']; ?>libs/md5/md5.min.js"></script>
    <script src="<?php echo $frontendConfig['assets_path']; ?>js/modal.js"></script>
    <script src="<?php echo $frontendConfig['assets_path']; ?>js/payment.js"></script>
    <script src="<?php echo $frontendConfig['assets_path']; ?>js/software.js"></script>

    <script>
    // 注册分类树组件
    Vue.component('category-tree', {
        template: '#category-tree-template',
        props: {
            categories: {
                type: Array,
                required: true
            },
            activeCategory: {
                type: String,
                default: 'all'
            },
            expandedCategories: {
                type: Array,
                default: () => []
            }
        },
        methods: {
            handleCategoryClick(category) {
                // 如果有子分类，则切换展开/折叠状态
                if (category.children && category.children.length > 0) {
                    this.toggleExpand(category.id);
                }

                // 无论是否有子分类，都选择该分类
                this.selectCategory(category);
            },
            selectCategory(category) {
                // 发送分类名称和ID
                this.$emit('select-category', category.name, category.id);
            },
            toggleExpand(categoryId) {
                this.$emit('toggle-expand', categoryId);
            },
            isExpanded(categoryId) {
                return this.expandedCategories.includes(categoryId);
            }
        }
    });

    const vueApp = new Vue({
        el: '#app',
        data: {
            searchTerm: '',
            categories: [],
            software: [],
            activeCategory: 'all',
            currentSoftware: null,
            expandedCategories: [], // 存储已展开的分类ID
            searchConfig: {
                mode: '<?php echo $searchMode; ?>', // 'instant' 或 'click'
                delay: <?php echo $searchDelay; ?> // 即时搜索的延迟时间（毫秒）
            },
            paginationConfig: {
                default_page_size: <?php echo $defaultPageSize; ?>,
                max_page_size: <?php echo $maxPageSize; ?>,
                show_total: <?php echo $showTotal ? 'true' : 'false'; ?>
            },
            pagination: {
                page: 1,
                pageSize: <?php echo $defaultPageSize; ?>,
                total: 0,
                pages: 1
            },
            debugEnabled: <?php echo $debugEnabled ? 'true' : 'false'; ?>, // 调试模式
            searchTimeout: null
        },
        computed: {
            totalSoftwareCount() {
                return this.software.length;
            },
            filteredSoftware() {
                // 直接返回从后端获取的软件列表，不再在前端进行过滤
                return this.software;
            }
        },
        methods: {
            // 切换分类的展开/折叠状态
            toggleCategoryExpand(categoryId) {
                const index = this.expandedCategories.indexOf(categoryId);
                if (index === -1) {
                    this.expandedCategories.push(categoryId);
                } else {
                    this.expandedCategories.splice(index, 1);
                }
            },
            getCategoryCount(categoryId) {
                return this.software.filter(s => s.category === categoryId).length;
            },
            getCategoryName(categoryId) {
                // 递归查找分类名称
                const findCategoryName = (categories, id) => {
                    for (const category of categories) {
                        if (category.id === id) {
                            return category.name;
                        }
                        if (category.children && category.children.length > 0) {
                            const name = findCategoryName(category.children, id);
                            if (name) return name;
                        }
                    }
                    return null;
                };

                const name = findCategoryName(this.categories, categoryId);
                return name || '未分类';
            },
            getDownloadUrl(softwareId, urlIndex = 1) {
                // 使用当前时间戳（秒级）
                const timestamp = Math.floor(Date.now() / 1000);

                // 构建令牌字符串：软件ID_时间戳
                const tokenStr = `${softwareId}_${timestamp}`;

                console.log('生成下载令牌:', tokenStr); // 调试信息

                // 使用MD5生成令牌
                let token = '';
                try {
                    // 使用JavaScript的MD5库，如果可用的话
                    if (typeof md5 === 'function') {
                        token = md5(tokenStr);
                    } else {
                        // 简单替代方案，非生产环境使用
                        console.error('MD5函数不可用，使用替代方案');
                        token = btoa(tokenStr);
                    }
                } catch (e) {
                    console.error('生成令牌出错:', e);
                    // 出错情况下的简单替代方案
                    token = btoa(tokenStr);
                }

                return `/api/public/download.php?id=${softwareId}&token=${token}&ts=${timestamp}&url_index=${urlIndex}`;
            },

            // 获取完整的URL（包含frontend_url）
            getFullUrl(relativeUrl) {
                // 如果已经是完整URL，直接返回
                if (relativeUrl.startsWith('http://') || relativeUrl.startsWith('https://') || relativeUrl.startsWith('//')) {
                    return relativeUrl;
                }

                // 获取frontend_url配置
                let frontendUrl = '';
                if (window.siteConfig && window.siteConfig.frontendUrl) {
                    frontendUrl = window.siteConfig.frontendUrl;
                } else {
                    // 如果没有配置，使用当前域名
                    frontendUrl = window.location.origin;
                }

                // 确保frontendUrl不以/结尾，relativeUrl以/开头
                frontendUrl = frontendUrl.replace(/\/$/, '');
                if (!relativeUrl.startsWith('/')) {
                    relativeUrl = '/' + relativeUrl;
                }

                return frontendUrl + relativeUrl;
            },

            // 处理购买操作
            handlePurchase(software) {
                // 存储当前下载的软件ID
                localStorage.setItem('current_download_software_id', software.id);

                // 显示支付选择弹窗
                showPaymentOptions(software);
            },

            // 显示下载选项弹窗
            showDownloadOptions(software) {
                // 检查必要的对象是否存在
                if (typeof universalModal === 'undefined') {
                    console.error('universalModal 未定义');
                    alert('弹窗系统未初始化，请刷新页面重试');
                    return;
                }

                this.currentSoftware = software;

                // 使用通用弹窗显示下载选项
                universalModal.show('download', {
                    software: software
                });
            },

            // 添加下载选项
            addDownloadOption(container, name, url, iconClass, description) {
                const option = document.createElement('div');
                option.className = 'download-option';

                const iconHtml = `
                    <div class="download-option-icon">
                        <i class="${iconClass}"></i>
                    </div>
                `;

                const textHtml = `
                    <div class="download-option-text">
                        <div class="download-option-name">${name}</div>
                        <div class="download-option-desc">${description}</div>
                    </div>
                `;

                const buttonsHtml = `
                    <div class="download-option-buttons">
                        <button class="download-copy-btn" data-url="${url}">复制链接</button>
                        <a href="${url}" class="download-now-btn" target="_blank">立即下载</a>
                    </div>
                `;

                option.innerHTML = iconHtml + textHtml + buttonsHtml;

                // 添加事件监听器
                option.querySelector('.download-copy-btn').addEventListener('click', (e) => {
                    e.stopPropagation();
                    // 复制完整的URL
                    const fullUrl = this.getFullUrl(url);
                    this.copyToClipboard(fullUrl);
                    alert('下载链接已复制到剪贴板');
                });

                option.querySelector('.download-now-btn').addEventListener('click', (e) => {
                    // 只阻止事件冒泡，不阻止默认行为，让链接正常在新标签页打开
                    e.stopPropagation();
                    // 关闭弹窗
                    //document.getElementById('downloadModal').classList.remove('active');
                });

                container.appendChild(option);
            },

            // 复制文本到剪贴板
            copyToClipboard(text) {
                const textarea = document.createElement('textarea');
                textarea.value = text;
                document.body.appendChild(textarea);
                textarea.select();
                document.execCommand('copy');
                document.body.removeChild(textarea);
            },
            async setCategory(categoryName, categoryId) {
                // 清空搜索框
                this.searchTerm = '';

                // 重置分页到第一页
                this.pagination.page = 1;

                this.activeCategory = categoryName;
                // 更新UI显示
                document.querySelectorAll('.category-item').forEach(item => {
                    if (item.getAttribute('data-category') === categoryName) {
                        item.classList.add('active');
                    } else {
                        item.classList.remove('active');
                    }
                });

                // 如果选择的是"全部软件"，不需要传递分类ID
                if (categoryName === 'all') {
                    await this.fetchData();
                    return;
                }

                // 如果没有直接传入分类ID，则查找选中分类的ID
                let actualCategoryId = categoryId;
                if (!actualCategoryId) {
                    const findCategoryId = (categories, name) => {
                        for (const category of categories) {
                            if (category.name === name) {
                                return category.id;
                            }
                            if (category.children && category.children.length > 0) {
                                const id = findCategoryId(category.children, name);
                                if (id) return id;
                            }
                        }
                        return null;
                    };

                    actualCategoryId = findCategoryId(this.categories, categoryName);
                }

                if (actualCategoryId) {
                    // 向后端发送请求，获取该分类及其子分类的软件
                    try {
                        // 使用flag=software参数，只获取软件列表
                        // 确保不超过最大页面大小限制
                        const pageSize = Math.min(this.pagination.pageSize, this.paginationConfig.max_page_size);
                        const url = `api/public/index.php?category=${actualCategoryId}&flag=software&page=${this.pagination.page}&pageSize=${pageSize}`;
                        const data = await apiGet(url);
                        if (data && data.success !== false) {
                            // 更新软件列表和分页信息
                            this.software = data.software || [];
                            this.pagination = data.pagination || this.pagination;
                        }
                    } catch (error) {
                        console.error('获取分类软件失败:', error);
                    }
                }
            },
            // 处理搜索输入
            onSearchInput() {
                // 如果不是即时搜索模式，直接返回
                if (this.searchConfig.mode !== 'instant') {
                    return;
                }

                // 清除之前的定时器
                if (this.searchTimeout) {
                    clearTimeout(this.searchTimeout);
                }

                // 如果延迟为0，立即搜索
                if (this.searchConfig.delay === 0) {
                    this.search();
                } else {
                    // 否则设置定时器
                    this.searchTimeout = setTimeout(() => {
                        this.search();
                    }, this.searchConfig.delay);
                }
            },

            async search() {
                // 向后端发送搜索请求
                try {
                    // 重置分类选择
                    this.activeCategory = 'all';
                    document.querySelectorAll('.category-item').forEach(item => {
                        if (item.getAttribute('data-category') === 'all') {
                            item.classList.add('active');
                        } else {
                            item.classList.remove('active');
                        }
                    });

                    // 重置分页到第一页
                    this.pagination.page = 1;

                    // 构建搜索URL
                    let url = 'api/public/index.php?flag=software';
                    // 确保不超过最大页面大小限制
                    const pageSize = Math.min(this.pagination.pageSize, this.paginationConfig.max_page_size);
                    url += `&page=${this.pagination.page}&pageSize=${pageSize}`;
                    if (this.searchTerm) {
                        url += `&search=${encodeURIComponent(this.searchTerm)}`;
                    }

                    const data = await apiGet(url);
                    if (data && data.success !== false) {
                        // 更新软件列表和分页信息
                        this.software = data.software || [];
                        this.pagination = data.pagination || this.pagination;
                    }
                } catch (error) {
                    console.error('搜索失败:', error);
                }
            },

            // 切换页码
            async changePage(page) {
                if (page < 1 || page > this.pagination.pages) {
                    return;
                }
                this.pagination.page = page;

                // 构建URL
                let url = 'api/public/index.php?flag=software';
                // 确保不超过最大页面大小限制
                const pageSize = Math.min(this.pagination.pageSize, this.paginationConfig.max_page_size);
                url += `&page=${page}&pageSize=${pageSize}`;

                if (this.searchTerm) {
                    url += `&search=${encodeURIComponent(this.searchTerm)}`;
                }

                if (this.activeCategory !== 'all') {
                    // 查找选中分类的ID
                    const findCategoryId = (categories, name) => {
                        for (const category of categories) {
                            if (category.name === name) {
                                return category.id;
                            }
                            if (category.children && category.children.length > 0) {
                                const id = findCategoryId(category.children, name);
                                if (id) return id;
                            }
                        }
                        return null;
                    };

                    const categoryId = findCategoryId(this.categories, this.activeCategory);
                    if (categoryId) {
                        url += `&category=${categoryId}`;
                    }
                }

                try {
                    const data = await apiGet(url);
                    if (data && data.success !== false) {
                        this.software = data.software || [];
                        this.pagination = data.pagination || this.pagination;
                    }
                } catch (error) {
                    console.error('切换页码失败:', error);
                }
            },

            // 加载配置
            async loadConfig() {
                try {
                    const config = await apiGet('api/public/index.php?flag=config');
                    if (this.debugEnabled) console.log('加载的配置:', config);

                    if (config) {
                        // 加载搜索配置
                        if (config.search) {
                            this.searchConfig = config.search;
                            if (this.debugEnabled) console.log('搜索配置:', this.searchConfig);
                        }

                        // 加载分页配置
                        if (config.pagination) {
                            if (this.debugEnabled) console.log('分页配置:', config.pagination);
                            this.paginationConfig = config.pagination;

                            // 确保将default_page_size转换为数字作为默认值
                            if (config.pagination.default_page_size !== undefined) {
                                this.pagination.pageSize = parseInt(config.pagination.default_page_size, 10) || 10;
                            } else {
                                this.pagination.pageSize = 10;
                            }

                            // 保存最大页面大小限制
                            if (config.pagination.max_page_size !== undefined) {
                                this.paginationConfig.max_page_size = parseInt(config.pagination.max_page_size, 10) || 100;
                            }

                            if (this.debugEnabled) console.log('设置分页大小:', this.pagination.pageSize);
                        }

                        // 加载调试配置
                        if (config.debug !== undefined) {
                            this.debugEnabled = !!config.debug;
                        }
                    }
                } catch (error) {
                    console.error('加载配置失败:', error);
                }
            },
            // 重新加载配置
            async reloadConfig() {
                if (this.debugEnabled) console.log('重新加载配置...');
                await this.loadConfig();
                alert('配置已重新加载');
            },

            // 重新加载数据
            async reloadData() {
                if (this.debugEnabled) console.log('重新加载数据...');
                await this.fetchData();
                alert('数据已重新加载');
            },

            async fetchData() {
                try {
                    // 从API获取数据，使用我们的API工具函数
                    // 使用flag=full参数，获取完整数据（包括分类、软件和配置）
                    // 确保不超过最大页面大小限制
                    const pageSize = Math.min(this.pagination.pageSize, this.paginationConfig.max_page_size);
                    const url = `api/public/index.php?flag=full&page=${this.pagination.page}&pageSize=${pageSize}`;
                    if (this.debugEnabled) console.log('请求URL:', url);
                    const data = await apiGet(url);
                    if (this.debugEnabled) console.log('获取的数据:', data);

                    // 如果返回了数据且成功
                    if (data && data.success !== false) {
                        // 加载分类和软件数据
                        this.categories = data.categories || [];
                        this.software = data.software || [];
                        if (this.debugEnabled) console.log('软件数量:', this.software.length);

                        // 加载分页信息
                        if (data.pagination) {
                            if (this.debugEnabled) console.log('返回的分页信息:', data.pagination);
                            this.pagination = data.pagination;
                            if (this.debugEnabled) console.log('更新后的分页信息:', this.pagination);
                        } else if (this.debugEnabled) {
                            console.log('API没有返回分页信息');
                        }

                        // 加载配置信息
                        if (data.config) {
                            if (this.debugEnabled) console.log('返回的配置信息:', data.config);

                            // 加载搜索配置
                            if (data.config.search) {
                                this.searchConfig = data.config.search;
                                if (this.debugEnabled) console.log('搜索配置:', this.searchConfig);
                            }

                            // 加载分页配置
                            if (data.config.pagination) {
                                if (this.debugEnabled) console.log('分页配置:', data.config.pagination);
                                this.paginationConfig = data.config.pagination;

                                // 确保将default_page_size转换为数字作为默认值
                                if (data.config.pagination.default_page_size !== undefined) {
                                    this.pagination.pageSize = parseInt(data.config.pagination.default_page_size, 10) || 10;
                                }

                                // 保存最大页面大小限制
                                if (data.config.pagination.max_page_size !== undefined) {
                                    this.paginationConfig.max_page_size = parseInt(data.config.pagination.max_page_size, 10) || 100;
                                }

                                if (this.debugEnabled) console.log('设置分页大小:', this.pagination.pageSize);
                            }

                            // 加载调试配置
                            if (data.config.debug !== undefined) {
                                this.debugEnabled = !!data.config.debug;
                                if (this.debugEnabled) console.log('调试模式:', this.debugEnabled ? '开启' : '关闭');
                            }
                        }

                        // 默认不展开任何分类
                        // 保持 expandedCategories 为空数组
                    } else if (data && data.redirect) {
                        // 如果需要重定向
                        window.location.href = data.redirect;
                    }
                } catch (error) {
                    console.error('获取数据失败:', error);
                }
            }
        },
        mounted() {
            console.log('Vue应用已挂载');

            // 检查必要的全局对象
            setTimeout(() => {
                console.log('检查全局对象:');
                console.log('- universalModal:', typeof universalModal);
                console.log('- paymentHandler:', typeof paymentHandler);
                console.log('- paymentConfig:', typeof paymentConfig);
            }, 100);

            // 只加载数据，数据中已包含配置信息
            this.fetchData();
        }
    });

    // 将Vue实例保存到全局变量，供URL路由使用
    window.vueApp = vueApp;

    document.addEventListener('DOMContentLoaded', function () {
        const audioPlayer = document.getElementById('bg-music');

        // 音频播放事件
        audioPlayer.addEventListener('play', function () {
            console.log('背景音乐开始播放');
        });

        // 音频结束事件
        audioPlayer.addEventListener('ended', function () {
            console.log('背景音乐播放完毕');
        });

        // 音频错误事件
        audioPlayer.addEventListener('error', function () {
            console.error('背景音乐加载失败');
        });

        // 新增的二维码弹窗控制代码
        const aboutBtn = document.getElementById('aboutBtn');
        const qrModal = document.getElementById('qrModal');
        const qrClose = document.getElementById('qrClose');

        // 点击关于我们按钮显示二维码弹窗
        aboutBtn.addEventListener('click', function () {
            qrModal.classList.add('active');
        });

        // 点击关闭按钮隐藏二维码弹窗
        qrClose.addEventListener('click', function () {
            qrModal.classList.remove('active');
        });

        // 点击弹窗外部也隐藏二维码弹窗
        qrModal.addEventListener('click', function (e) {
            if (e.target === qrModal) {
                qrModal.classList.remove('active');
            }
        });

        // 进入弹窗公告控制代码
        const announcementModal = document.getElementById('announcementModal');
        const announcementClose = document.getElementById('announcementClose');
        const announcementConfirm = document.getElementById('announcementConfirm');

        // 检查是否需要显示公告
        function checkShouldShowAnnouncement() {
            if (!<?php echo json_encode($announcementEnabled); ?>) {
                return false;
            }

            // 获取本地存储的公告时间戳
            const storedTimestamp = localStorage.getItem('announcement_timestamp');
            const currentTimestamp = <?php echo json_encode($announcementTimestamp); ?>;
            const repeatShow = <?php echo json_encode($announcementRepeatShow); ?>;

            // 如果是重复显示模式，或者没有存储过时间戳，或者时间戳不匹配（公告已更新），则显示公告
            return repeatShow || !storedTimestamp || parseInt(storedTimestamp) !== currentTimestamp;
        }

        // 显示公告
        function showAnnouncement() {
            if (checkShouldShowAnnouncement()) {
                announcementModal.classList.add('active');
            }
        }

        // 记录已查看公告
        function markAnnouncementAsViewed() {
            // 存储当前公告时间戳到localStorage
            localStorage.setItem('announcement_timestamp', <?php echo json_encode($announcementTimestamp); ?>);
            announcementModal.classList.remove('active');
        }

        // 点击关闭按钮隐藏进入弹窗公告
        announcementClose.addEventListener('click', function () {
            markAnnouncementAsViewed();
        });

        // 点击确认按钮隐藏进入弹窗公告
        announcementConfirm.addEventListener('click', function () {
            markAnnouncementAsViewed();
        });

        // 点击弹窗外部也隐藏进入弹窗公告
        announcementModal.addEventListener('click', function (e) {
            if (e.target === announcementModal) {
                markAnnouncementAsViewed();
            }
        });

        // 页面加载时检查URL路由和公告
        setTimeout(() => {
            // 检查URL路由，如果是软件直链则不显示公告
            if (!checkUrlRouting()) {
                showAnnouncement();
            }
        }, 1000); // 延迟1秒显示公告

        // 监听hash变化，支持浏览器前进后退
        window.addEventListener('hashchange', function() {
            console.log('Hash变化:', window.location.hash);
            checkUrlRouting();
        });

        // URL路由检查函数
        function checkUrlRouting() {
            const hash = window.location.hash;

            // 检查是否是软件直链格式：#/software/12345
            const softwareMatch = hash.match(/^#\/software\/(\d+)$/);
            if (softwareMatch) {
                const softwareId = parseInt(softwareMatch[1]);
                console.log('检测到软件直链，软件ID:', softwareId);

                // 延迟一下确保Vue实例已经初始化
                setTimeout(() => {
                    handleSoftwareDirectLink(softwareId);
                }, 500);

                return true; // 返回true表示处理了路由，不显示公告
            }

            return false; // 返回false表示没有特殊路由，可以显示公告
        }

        // 处理软件直链
        async function handleSoftwareDirectLink(softwareId) {
            try {
                // 正常的软件直链访问
                await showSoftwareDownloadOptions(softwareId);
            } catch (error) {
                console.error('处理软件直链失败:', error);
                alert('处理软件直链失败，请稍后重试');
            }
        }



        // 显示软件下载选项
        async function showSoftwareDownloadOptions(softwareId) {
            try {
                // 获取软件信息
                const data = await apiGet(`/api/public/index.php?software_id=${softwareId}`);

                if (data && data.success && data.software) {
                    const software = data.software;
                    console.log('获取到软件信息:', software);

                    // 检查Vue实例是否存在
                    if (window.vueApp && window.vueApp.showDownloadOptions) {
                        // 直接显示下载选项弹窗
                        window.vueApp.showDownloadOptions(software);
                    } else {
                        console.error('Vue实例未找到，无法显示下载选项');
                    }
                } else {
                    console.error('未找到软件信息，软件ID:', softwareId);
                    // 可以显示错误提示
                    alert('未找到指定的软件');
                }
            } catch (error) {
                console.error('获取软件信息失败:', error);
                alert('获取软件信息失败，请稍后重试');
            }
        }

        // 监听支付成功事件
        document.addEventListener('payment:success', function(event) {
            const { orderNo, softwareId } = event.detail;
            console.log('支付成功事件触发:', { orderNo, softwareId });

            // 如果有软件ID，可以在这里添加额外的处理逻辑
            if (softwareId) {
                console.log('支付成功，软件ID:', softwareId, '订单号:', orderNo);
                // 这里可以添加其他需要在支付成功后执行的逻辑
                // 比如刷新软件列表、显示额外提示等
            }
        });

        // 下载选项弹窗控制代码
        const downloadModal = document.getElementById('downloadModal');
        // 下载选项弹窗现在使用通用弹窗管理器，不需要单独的事件监听器
    });
        // 全局变量存储当前会话的验证状态
        window.isAccessVerified = false;
        window.currentAccessPassword = null;

        /**
         * 检查是否需要访问密码验证
         * @returns {boolean} 是否需要验证
         */
        function checkAccessPassword() {
            // 检查是否设置了访问密码
            const accessPassword = '<?php echo addslashes($settings['security']['access_password'] ?? ''); ?>';

            // 如果没有设置访问密码，不需要验证
            if (!accessPassword) {
                return false;
            }

            // 检查当前会话是否已验证
            return !window.isAccessVerified;
        }

        /**
         * 显示访问密码验证弹窗
         */
        function showAccessPasswordModal() {
            const modal = document.getElementById('access-password-modal');
            const input = document.getElementById('access-password-input');
            const status = document.getElementById('access-password-status');

            modal.style.display = 'flex';
            status.textContent = '等待验证...';
            status.className = 'status-text';

            // 聚焦到密码输入框
            setTimeout(() => {
                input.focus();
            }, 100);
        }

        /**
         * 隐藏访问密码验证弹窗
         */
        function hideAccessPasswordModal() {
            const modal = document.getElementById('access-password-modal');
            modal.style.display = 'none';
        }

        /**
         * 验证访问密码
         */
        async function verifyAccessPassword() {
            const input = document.getElementById('access-password-input');
            const submitBtn = document.getElementById('access-password-submit');
            const status = document.getElementById('access-password-status');

            const password = input.value.trim();
            if (!password) {
                status.textContent = '请输入密码';
                status.className = 'status-text error';
                return;
            }

            submitBtn.disabled = true;
            status.textContent = '验证中...';
            status.className = 'status-text loading';

            try {
                // 临时设置密码，用于验证
                const tempPassword = password;

                // 尝试请求主数据接口来验证密码（使用原来的参数）
                const response = await fetch('/api/public/index.php', {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'X-Access-Password': tempPassword
                    }
                });

                // 检查响应状态码
                if (response.status === 200) {
                    const data = await response.json();

                    // 检查是否验证成功（success为true且有数据内容）
                    if (data && data.success !== false && (data.software || data.categories)) {
                        status.textContent = '✓ ' + (data.message || '验证成功，正在加载内容...');
                        status.className = 'status-text success';

                        // 设置验证状态和保存密码
                        window.isAccessVerified = true;
                        window.currentAccessPassword = password;

                        // 直接使用返回的数据更新Vue应用
                        if (typeof window.vueApp !== 'undefined') {
                            if (data.categories) {
                                window.vueApp.categories = data.categories;
                            }
                            if (data.software) {
                                window.vueApp.software = data.software;
                            }
                            if (data.pagination) {
                                window.vueApp.pagination = data.pagination;
                            }
                        }

                        // 隐藏弹窗
                        setTimeout(() => {
                            hideAccessPasswordModal();
                            // 清空输入框
                            input.value = '';
                        }, 1000);
                    } else {
                        // 返回200但验证失败或没有数据
                        status.textContent = '✗ ' + (data && data.message ? data.message : '验证失败，请检查密码');
                        status.className = 'status-text error';
                        submitBtn.disabled = false;

                        // 清空密码输入框并重新聚焦
                        input.value = '';
                        input.focus();
                    }
                } else {
                    // 状态码不是200，尝试解析错误信息
                    try {
                        const errorData = await response.json();
                        status.textContent = '✗ ' + (errorData.message || '密码错误');
                    } catch (e) {
                        status.textContent = '✗ 密码错误';
                    }
                    status.className = 'status-text error';
                    submitBtn.disabled = false;

                    // 清空密码输入框并重新聚焦
                    input.value = '';
                    input.focus();
                }
            } catch (error) {
                console.error('验证请求失败:', error);
                status.textContent = '✗ 验证请求失败，请重试';
                status.className = 'status-text error';
                submitBtn.disabled = false;
            }
        }

        // 页面加载时检查访问密码验证
        document.addEventListener('DOMContentLoaded', function() {
            if (checkAccessPassword()) {
                // 需要验证，显示弹窗
                showAccessPasswordModal();
            }

            // 绑定验证按钮事件
            const submitBtn = document.getElementById('access-password-submit');
            const input = document.getElementById('access-password-input');

            submitBtn.addEventListener('click', verifyAccessPassword);

            // 回车键提交
            input.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    verifyAccessPassword();
                }
            });

            // 将弹窗函数暴露到全局作用域
            window.showAccessPasswordModal = showAccessPasswordModal;
        });
    </script>

    <!-- 访问密码验证弹窗 -->
    <div id="access-password-modal" class="modal-overlay" style="display: none;">
        <div class="access-password-modal">
            <div class="access-password-header">
                <h2>加密安全验证</h2>
            </div>
            <div class="access-password-body">
                <div class="password-group">
                    <label class="password-label">请输入访问密码</label>
                    <input type="password" id="access-password-input" class="password-input" placeholder="输入密码" autocomplete="off" autofocus>
                </div>
                <button type="button" id="access-password-submit" class="submit-btn">验证</button>
                <div class="status-text" id="access-password-status">等待验证...</div>
            </div>
        </div>
    </div>
</body>
</html>