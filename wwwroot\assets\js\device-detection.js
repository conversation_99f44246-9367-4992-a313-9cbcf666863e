/**
 * 设备检测和跳转模块
 * 
 * 该模块提供设备类型检测功能，并根据后台配置执行相应的跳转操作
 */

// 定义目录分隔符常量
if (!window.DS) {
    window.DS = '/';
}

/**
 * 设备检测类
 */
class DeviceDetection {
    constructor() {
        this.userAgent = navigator.userAgent || "";
        this.platform = navigator.platform || "";
        this.isTouch = 'ontouchend' in document;
    }

    /**
     * 获取设备类型键名
     * @returns {string} 设备类型键名
     */
    getDeviceKey() {
        const ua = this.userAgent;
        const platform = this.platform;
        const isTouch = this.isTouch;

        // iOS设备检测
        if (/iPhone|iPod/.test(ua)) {
            return "ios_phone";
        }
        if (/iPad/.test(ua)) {
            return "ios_tablet";
        }
        // iPadOS 13+ 伪装成 macOS 的检测
        if (/Macintosh/.test(ua) && isTouch) {
            return "ios_tablet";
        }

        // Android设备检测
        if (/Android/.test(ua)) {
            if (/Mobile/.test(ua)) {
                return "android_phone";
            }
            if (/X11/.test(ua)) {
                return "android_desktop";
            }
            return "android_tablet";
        }

        // Windows设备检测
        if (/Windows Phone/.test(ua)) {
            return "windows_phone";
        }
        if (/Windows/.test(ua)) {
            if (/Touch/.test(ua)) {
                return "windows_tablet";
            }
            return "windows_desktop";
        }

        // macOS桌面检测
        if (/Mac/.test(platform)) {
            return "macos_desktop";
        }

        // Linux桌面检测
        if (/Linux/.test(platform)) {
            return "linux_desktop";
        }

        // 未知设备
        return "unknown";
    }

    /**
     * 获取设备类型的友好名称
     * @returns {string} 设备类型的友好名称
     */
    getDeviceName() {
        const deviceKey = this.getDeviceKey();
        const deviceNames = {
            'ios_phone': 'iPhone',
            'ios_tablet': 'iPad',
            'android_phone': 'Android手机',
            'android_tablet': 'Android平板',
            'android_desktop': 'Android桌面',
            'windows_phone': 'Windows Phone',
            'windows_tablet': 'Windows平板',
            'windows_desktop': 'Windows桌面',
            'macos_desktop': 'macOS桌面',
            'linux_desktop': 'Linux桌面',
            'unknown': '未知设备'
        };
        return deviceNames[deviceKey] || '未知设备';
    }

    /**
     * 检查是否为移动设备
     * @returns {boolean} 是否为移动设备
     */
    isMobile() {
        const deviceKey = this.getDeviceKey();
        return ['ios_phone', 'android_phone', 'windows_phone'].includes(deviceKey);
    }

    /**
     * 检查是否为平板设备
     * @returns {boolean} 是否为平板设备
     */
    isTablet() {
        const deviceKey = this.getDeviceKey();
        return ['ios_tablet', 'android_tablet', 'windows_tablet'].includes(deviceKey);
    }

    /**
     * 检查是否为桌面设备
     * @returns {boolean} 是否为桌面设备
     */
    isDesktop() {
        const deviceKey = this.getDeviceKey();
        return ['android_desktop', 'windows_desktop', 'macos_desktop', 'linux_desktop'].includes(deviceKey);
    }

    /**
     * 检查是否为苹果设备（兼容旧版本）
     * @returns {boolean} 是否为苹果设备
     */
    isAppleDevice() {
        return /Mac|iPod|iPhone|iPad/.test(this.platform) || 
               (/Macintosh/.test(this.userAgent) && this.isTouch);
    }
}

/**
 * 设备跳转管理器
 */
class DeviceRedirectManager {
    constructor() {
        this.deviceDetection = new DeviceDetection();
        this.redirectConfig = null;
        this.debugMode = false;
    }

    /**
     * 设置调试模式
     * @param {boolean} enabled 是否启用调试模式
     */
    setDebugMode(enabled) {
        this.debugMode = enabled;
        if (this.debugMode) {
            console.log('[设备跳转] 调试模式已启用');
        }
    }

    /**
     * 设置跳转配置
     * @param {Object} config 跳转配置对象
     */
    setRedirectConfig(config) {
        this.redirectConfig = config;
        if (this.debugMode) {
            console.log('[设备跳转] 配置已设置:', config);
        }
    }

    /**
     * 执行设备检测和跳转
     */
    executeRedirect() {
        if (!this.redirectConfig) {
            if (this.debugMode) {
                console.log('[设备跳转] 未设置跳转配置，跳过检测');
            }
            return;
        }

        // 检查是否启用设备跳转
        if (!this.redirectConfig.enabled) {
            if (this.debugMode) {
                console.log('[设备跳转] 设备跳转功能已禁用');
            }
            return;
        }

        // 获取当前设备类型
        const deviceKey = this.deviceDetection.getDeviceKey();
        const deviceName = this.deviceDetection.getDeviceName();
        
        if (this.debugMode) {
            console.log('[设备跳转] 检测到设备类型:', deviceKey, '(' + deviceName + ')');
        }

        // 获取对应的跳转URL
        const redirectUrl = this.redirectConfig[deviceKey];

        if (!redirectUrl || redirectUrl.trim() === '') {
            if (this.debugMode) {
                console.log('[设备跳转] 设备类型', deviceKey, '未配置跳转URL或URL为空');
            }
            return;
        }

        if (this.debugMode) {
            console.log('[设备跳转] 准备跳转到:', redirectUrl);
        }

        // 执行跳转
        try {
            window.location.href = redirectUrl;
        } catch (error) {
            console.error('[设备跳转] 跳转失败:', error);
        }
    }

    /**
     * 获取设备信息（用于调试）
     * @returns {Object} 设备信息对象
     */
    getDeviceInfo() {
        return {
            deviceKey: this.deviceDetection.getDeviceKey(),
            deviceName: this.deviceDetection.getDeviceName(),
            userAgent: this.deviceDetection.userAgent,
            platform: this.deviceDetection.platform,
            isTouch: this.deviceDetection.isTouch,
            isMobile: this.deviceDetection.isMobile(),
            isTablet: this.deviceDetection.isTablet(),
            isDesktop: this.deviceDetection.isDesktop(),
            isAppleDevice: this.deviceDetection.isAppleDevice()
        };
    }
}

// 确保在脚本加载完成后立即创建全局实例
(function() {
    // 创建全局实例
    window.deviceDetection = new DeviceDetection();
    window.deviceRedirectManager = new DeviceRedirectManager();

    // 兼容旧版本的函数
    window.isAppleDevice = function() {
        return window.deviceDetection.isAppleDevice();
    };

    window.getDeviceKey = function() {
        return window.deviceDetection.getDeviceKey();
    };

    // 标记模块已加载
    window.deviceDetectionLoaded = true;

    // 如果有等待的回调函数，立即执行
    if (window.deviceDetectionCallbacks) {
        window.deviceDetectionCallbacks.forEach(function(callback) {
            try {
                callback();
            } catch (error) {
                console.error('[设备检测] 回调执行失败:', error);
            }
        });
        window.deviceDetectionCallbacks = [];
    }
})();

// 导出模块（如果支持模块系统）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        DeviceDetection,
        DeviceRedirectManager
    };
}
