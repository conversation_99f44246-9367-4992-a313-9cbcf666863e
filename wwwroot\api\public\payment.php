<?php
// 定义目录分隔符常量
if (!defined('DS')) {
    define('DS', DIRECTORY_SEPARATOR);
}
/**
 * 支付接口
 */

// 包含公共API验证文件
require_once dirname(__DIR__) . DS . 'public.php';

// 检查访问密码验证
check_access_password();

// 包含支付函数库
require_once dirname(__DIR__) . DS . 'payment.php';

// 获取数据库连接
$db = get_db_connection();

// 处理请求
$method = $_SERVER['REQUEST_METHOD'];

try {
    switch ($method) {
        case 'GET':
            // 获取支付状态
            if (isset($_GET['order_no'])) {
                $orderNo = $_GET['order_no'];

                // 查询订单
                $stmt = $db->prepare('SELECT * FROM payment_orders WHERE order_no = :order_no');
                $stmt->bindValue(':order_no', $orderNo, SQLITE3_TEXT);
                $result = $stmt->execute();
                $order = $result->fetchArray(SQLITE3_ASSOC);

                if (!$order) {
                    json_response(['success' => false, 'message' => '订单不存在'], 404);
                }

                json_response([
                    'success' => true,
                    'data' => [
                        'order_no' => $order['order_no'],
                        'status' => $order['status'],
                        'payment_type' => $order['payment_type'],
                        'amount' => $order['amount'],
                        'created_at' => $order['created_at'],
                        'paid_at' => $order['paid_at']
                    ]
                ]);
            } else {
                // 获取支付配置
                $paymentConfig = get_payment_config();

                if (!$paymentConfig || !$paymentConfig['enabled']) {
                    json_response(['success' => false, 'message' => '支付功能未启用'], 400);
                }

                // 返回可用的支付方式
                $availablePayments = [];

                if (isset($paymentConfig['wechat_pay']) && $paymentConfig['wechat_pay']['enabled']) {
                    $availablePayments[] = 'wechat_pay';
                }

                if (isset($paymentConfig['alipay']) && $paymentConfig['alipay']['enabled']) {
                    $availablePayments[] = 'alipay';
                }

                json_response([
                    'success' => true,
                    'data' => [
                        'enabled' => true,
                        'available_payments' => $availablePayments,
                        'is_mobile' => is_mobile_device()
                    ]
                ]);
            }
            break;

        case 'POST':
            // 创建支付订单
            $json = file_get_contents('php://input');
            $data = json_decode($json, true);

            // 验证必填字段
            if (empty($data['software_id']) || empty($data['payment_type'])) {
                json_response(['success' => false, 'message' => '缺少必要参数'], 400);
            }

            // 检查支付类型是否有效
            $paymentType = $data['payment_type'];
            if ($paymentType !== 'wechat_pay' && $paymentType !== 'alipay') {
                json_response(['success' => false, 'message' => '不支持的支付类型'], 400);
            }

            // 检查支付配置
            $paymentConfig = get_payment_config($paymentType);
            if (!$paymentConfig) {
                json_response(['success' => false, 'message' => '该支付方式未启用'], 400);
            }

            // 获取软件信息
            $softwareId = intval($data['software_id']);
            $stmt = $db->prepare('SELECT * FROM softwares WHERE id = :id');
            $stmt->bindValue(':id', $softwareId, SQLITE3_INTEGER);
            $result = $stmt->execute();
            $software = $result->fetchArray(SQLITE3_ASSOC);

            if (!$software) {
                json_response(['success' => false, 'message' => '软件不存在'], 404);
            }

            // 检查软件是否需要付费
            if (empty($software['price']) || floatval($software['price']) <= 0) {
                json_response(['success' => false, 'message' => '该软件为免费软件，无需支付'], 400);
            }

            // 生成订单号
            $orderNo = date('YmdHis') . mt_rand(1000, 9999);

            // 创建订单记录
            $stmt = $db->prepare('
                INSERT INTO payment_orders (
                    order_no, software_id, user_ip, amount, payment_type, status, created_at
                ) VALUES (
                    :order_no, :software_id, :user_ip, :amount, :payment_type, :status, :created_at
                )
            ');

            $stmt->bindValue(':order_no', $orderNo, SQLITE3_TEXT);
            $stmt->bindValue(':software_id', $softwareId, SQLITE3_INTEGER);
            $stmt->bindValue(':user_ip', get_client_ip(), SQLITE3_TEXT);
            $stmt->bindValue(':amount', floatval($software['price']), SQLITE3_FLOAT);
            $stmt->bindValue(':payment_type', $paymentType, SQLITE3_TEXT);
            $stmt->bindValue(':status', 'pending', SQLITE3_TEXT);
            $stmt->bindValue(':created_at', time(), SQLITE3_INTEGER);
            $stmt->execute();

            // 创建支付订单
            $orderData = [
                'out_trade_no' => $orderNo,
                'total_fee' => floatval($software['price']),
                'total_amount' => floatval($software['price']),
                'body' => '购买软件：' . $software['name'],
                'subject' => '购买软件：' . $software['name']
            ];

            // 根据支付类型创建订单
            if ($paymentType === 'wechat_pay') {
                $result = create_wechat_pay_order($orderData);
            } else {
                $result = create_alipay_order($orderData);
            }

            // 返回支付信息
            json_response($result);
            break;

        default:
            json_response(['success' => false, 'message' => '不支持的请求方法'], 405);
    }
} catch (Exception $e) {
    json_response([
        'success' => false,
        'message' => '操作失败: ' . $e->getMessage()
    ], 500);
}

