<?php
// 定义目录分隔符常量
if (!defined('DS')) {
    define('DS', DIRECTORY_SEPARATOR);
}

// 包含公共函数库
require_once __DIR__ . DS . 'common.php';

// 加载站点设置
$settings = get_settings();

// 获取访问密码设置
$accessPassword = $settings['security']['access_password'] ?? '';

// 如果没有设置密码，直接重定向到首页
if (empty($accessPassword)) {
    header('Location: /');
    exit;
}

// 获取站点信息
$siteTitle = $settings['basic']['site_title'] ?? '娜宝贝软件';
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <?php if (!empty($settings['basic']['site_favicon'])): ?>
    <link rel="icon" type="image/x-icon" href="<?php echo htmlspecialchars($settings['basic']['site_favicon']); ?>">
    <?php endif; ?>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>加密安全验证 - <?php echo htmlspecialchars($siteTitle); ?></title>
    <style>
        /* 基础重置 - 确保多浏览器一致性 */
        html, body {
            margin: 0;
            padding: 0;
            height: 100%;
            overflow: hidden;
            font-family: Arial, "Microsoft YaHei", sans-serif;
        }
        
        body {
            background-color: #0a192f;
            color: #e6f1ff;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        /* 蓝色数字雨效果 */
        .matrix-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 0;
            opacity: 0.15;
            pointer-events: none;
        }
        
        /* 蓝色主题验证窗口 */
        .auth-box {
            position: relative;
            width: 320px;
            padding: 30px;
            background-color: rgba(16, 42, 87, 0.85);
            border: 1px solid rgba(100, 200, 255, 0.3);
            border-radius: 5px;
            box-shadow: 0 0 15px rgba(100, 200, 255, 0.4),
                        inset 0 0 10px rgba(100, 200, 255, 0.2);
            z-index: 1;
        }
        
        .auth-title {
            text-align: center;
            margin-bottom: 25px;
            font-size: 20px;
            text-shadow: 0 0 8px rgba(100, 200, 255, 0.6);
        }
        
        .password-group {
            margin-bottom: 20px;
        }
        
        .password-label {
            display: block;
            margin-bottom: 8px;
            font-size: 14px;
            color: #a8b2d1;
        }
        
        .password-input {
            width: 100%;
            padding: 12px 15px;
            background-color: rgba(10, 25, 47, 0.8);
            border: 1px solid rgba(100, 200, 255, 0.3);
            color: #e6f1ff;
            border-radius: 4px;
            font-size: 16px;
            outline: none;
            box-sizing: border-box;
        }
        
        .password-input:focus {
            border-color: #64ffda;
            box-shadow: 0 0 8px rgba(100, 255, 218, 0.5);
        }
        
        .submit-btn {
            width: 100%;
            padding: 12px;
            background-color: #1e90ff;
            border: none;
            color: white;
            font-weight: bold;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s;
        }
        
        .submit-btn:hover {
            background-color: #0077ff;
            box-shadow: 0 0 10px rgba(30, 144, 255, 0.7);
        }
        
        .submit-btn:disabled {
            background-color: #666;
            cursor: not-allowed;
        }
        
        .status-text {
            margin-top: 15px;
            min-height: 20px;
            text-align: center;
            font-size: 14px;
        }
        
        /* 加载动画 */
        @keyframes fadePulse {
            0%, 100% { opacity: 0.6; }
            50% { opacity: 1; }
        }
        
        .loading {
            animation: fadePulse 1.5s infinite;
            color: #64ffda;
        }
        
        .success {
            color: #64ffda;
        }
        
        .error {
            color: #ff5555;
        }
    </style>
</head>
<body>
    <!-- 蓝色数字雨背景 -->
    <canvas id="matrix" class="matrix-bg"></canvas>
    
    <!-- 验证窗口 -->
    <div class="auth-box">
        <h2 class="auth-title">加密安全验证</h2>
        
        <form id="verifyForm">
            <div class="password-group">
                <label class="password-label">请输入访问密码</label>
                <input type="password" class="password-input" id="passwordInput" name="password" placeholder="输入密码" autocomplete="off" autofocus required>
            </div>
            
            <button type="submit" class="submit-btn" id="submitBtn">验证</button>
        </form>
        
        <div class="status-text" id="statusText">等待验证...</div>
    </div>

    <script>
        // 蓝色数字雨效果
        function initMatrix() {
            var canvas = document.getElementById('matrix');
            if (!canvas.getContext) return;
            
            var ctx = canvas.getContext('2d');
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
            
            var chars = "01";
            chars = chars.split("");
            
            var font_size = 14;
            var columns = canvas.width / font_size;
            var drops = [];
            
            for (var x = 0; x < columns; x++)
                drops[x] = 1; 
            
            function draw() {
                ctx.fillStyle = "rgba(10, 25, 47, 0.05)";
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                
                ctx.fillStyle = "#64ffda";
                ctx.font = font_size + "px arial";
                
                for (var i = 0; i < drops.length; i++) {
                    var text = chars[Math.floor(Math.random() * chars.length)];
                    ctx.fillText(text, i * font_size, drops[i] * font_size);
                    
                    if (drops[i] * font_size > canvas.height && Math.random() > 0.975)
                        drops[i] = 0;
                    
                    drops[i]++;
                }
            }
            
            setInterval(draw, 33);
        }
        
        // 页面加载完成后初始化
        window.onload = function() {
            initMatrix();
            
            // 自动聚焦密码输入框
            document.getElementById('passwordInput').focus();
            
            // 表单提交处理
            document.getElementById('verifyForm').addEventListener('submit', function(e) {
                e.preventDefault();
                verifyPassword();
            });
            
            // 验证函数
            function verifyPassword() {
                var password = document.getElementById('passwordInput').value;
                var statusText = document.getElementById('statusText');
                var submitBtn = document.getElementById('submitBtn');
                
                if (!password) {
                    statusText.textContent = "请输入密码";
                    statusText.className = "status-text error";
                    return;
                }
                
                statusText.textContent = "验证中...";
                statusText.className = "status-text loading";
                submitBtn.disabled = true;
                
                // 发送验证请求到API
                fetch('/api/public/verify_access.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        password: password
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        statusText.textContent = "✓ 验证成功，正在进入系统...";
                        statusText.className = "status-text success";

                        // 保存访问令牌到localStorage
                        if (data.access_token) {
                            localStorage.setItem('access_token', data.access_token);
                        }

                        // 验证成功后跳转到首页
                        setTimeout(function() {
                            window.location.href = "/";
                        }, 1000);
                    } else {
                        statusText.textContent = "✗ " + (data.message || "密码错误");
                        statusText.className = "status-text error";
                        submitBtn.disabled = false;

                        // 清空密码输入框
                        document.getElementById('passwordInput').value = '';
                        document.getElementById('passwordInput').focus();
                    }
                })
                .catch(error => {
                    console.error('验证请求失败:', error);
                    statusText.textContent = "✗ 验证请求失败，请重试";
                    statusText.className = "status-text error";
                    submitBtn.disabled = false;
                });
            }
            
            // 窗口大小变化时重绘背景
            window.addEventListener('resize', function() {
                var canvas = document.getElementById('matrix');
                if (canvas) {
                    canvas.width = window.innerWidth;
                    canvas.height = window.innerHeight;
                }
            });
        };
    </script>
</body>
</html>
