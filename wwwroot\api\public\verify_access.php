<?php
// 定义目录分隔符常量
if (!defined('DS')) {
    define('DS', DIRECTORY_SEPARATOR);
}

// 包含公共API文件（但跳过访问密码检查）
require_once dirname(__DIR__) . DS . 'common.php';

// 检查是否已安装
check_installed(true);

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    api_error('只允许POST请求', 405);
}

// 获取请求数据
$input = json_decode(file_get_contents('php://input'), true);
if (!$input) {
    api_error('无效的JSON数据');
}

$password = $input['password'] ?? '';
if (empty($password)) {
    api_error('密码不能为空');
}

// 获取站点设置
$settings = get_settings();
$accessPassword = $settings['security']['access_password'] ?? '';

// 如果没有设置访问密码，返回成功（不需要验证）
if (empty($accessPassword)) {
    api_success([
        'message' => '无需验证',
        'access_token' => null
    ]);
}

// 验证密码
if ($password !== $accessPassword) {
    api_error('密码错误', 401);
}

// 生成访问令牌
$accessToken = generate_access_token();

// 返回成功响应
api_success([
    'message' => '验证成功',
    'access_token' => $accessToken
]);

/**
 * 生成访问令牌
 * 
 * @return string
 */
function generate_access_token() {
    // 使用当前时间戳、随机数和站点密钥生成令牌
    $settings = get_settings();
    $siteKey = $settings['basic']['site_title'] ?? 'default_site';
    $timestamp = time();
    $random = bin2hex(random_bytes(16));
    
    // 生成令牌（有效期24小时）
    $payload = [
        'timestamp' => $timestamp,
        'expires' => $timestamp + 86400, // 24小时后过期
        'random' => $random
    ];
    
    $payloadJson = json_encode($payload);
    $signature = hash_hmac('sha256', $payloadJson, $siteKey);
    
    return base64_encode($payloadJson . '.' . $signature);
}
