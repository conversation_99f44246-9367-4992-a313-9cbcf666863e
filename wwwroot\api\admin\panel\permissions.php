<?php
// 定义目录分隔符常量
if (!defined('DS')) {
    define('DS', DIRECTORY_SEPARATOR);
}
// 包含管理员面板验证文件
require_once dirname(__DIR__) . DS . 'panel.php';

// 获取数据库连接
$db = get_db_connection();

// 处理请求
$method = $_SERVER['REQUEST_METHOD'];

// 所有可用权限的定义
$availablePermissions = [
    'admin' => [
        'admin.view' => '查看管理员列表',
        'admin.add' => '添加管理员',
        'admin.edit' => '编辑管理员',
        'admin.delete' => '删除管理员'
    ],
    'role' => [
        'role.view' => '查看角色列表',
        'role.add' => '添加角色',
        'role.edit' => '编辑角色',
        'role.delete' => '删除角色'
    ],
    'software' => [
        'software.view' => '查看软件列表',
        'software.add' => '添加软件',
        'software.edit' => '编辑软件',
        'software.delete' => '删除软件'
    ],
    'category' => [
        'category.view' => '查看分类列表',
        'category.add' => '添加分类',
        'category.edit' => '编辑分类',
        'category.delete' => '删除分类'
    ],
    'orders' => [
        'orders.view' => '查看订单列表',
        'orders.edit' => '编辑订单状态',
        'orders.delete' => '删除订单'
    ],
    'payment_notify' => [
        'payment_notify.view' => '查看支付通知记录',
        'payment_notify.verify' => '手动验签支付通知'
    ],
    'stats' => [
        'stats.view' => '查看系统统计',
    ],
    'admin_settings' => [
        'admin.settings' => '管理站点设置'
    ],
    'system' => [
        'all' => '所有权限（超级管理员）'
    ]
];

try {
    switch ($method) {
        case 'GET':
            // 检查当前管理员的权限和角色
            if (isset($_GET['check'])) {
                $admin = [
                    'id' => $adminId,
                    'username' => $adminUsername,
                    'permissions' => []
                ];

                // 在救援模式下，拥有所有权限
                if (isset($_SESSION['admin_rescue_mode']) && $_SESSION['admin_rescue_mode']) {
                    $admin['is_rescue_mode'] = true;
                    $admin['role_id'] = 0;
                    $admin['role_name'] = '救援模式';

                    // 所有权限
                    foreach ($availablePermissions as $category => $perms) {
                        foreach ($perms as $perm => $desc) {
                            $admin['permissions'][] = $perm;
                        }
                    }
                } else {
                    // 获取管理员角色
                    $stmt = $db->prepare('
                        SELECT a.role_id, r.name as role_name, r.permissions
                        FROM admins a
                        JOIN admin_roles r ON a.role_id = r.id
                        WHERE a.id = :admin_id
                    ');
                    $stmt->bindValue(':admin_id', $adminId, SQLITE3_INTEGER);
                    $result = $stmt->execute();
                    $roleData = $result->fetchArray(SQLITE3_ASSOC);

                    if ($roleData) {
                        $admin['role_id'] = $roleData['role_id'];
                        $admin['role_name'] = $roleData['role_name'];

                        $permissions = json_decode($roleData['permissions'], true);

                        // 如果有all权限，添加所有权限
                        if (in_array('all', $permissions)) {
                            foreach ($availablePermissions as $category => $perms) {
                                foreach ($perms as $perm => $desc) {
                                    $admin['permissions'][] = $perm;
                                }
                            }
                        } else {
                            $admin['permissions'] = $permissions;
                        }
                    }
                }

                json_response(['success' => true, 'data' => $admin]);
            }
            // 获取所有可用权限定义
            else {
                json_response(['success' => true, 'data' => $availablePermissions]);
            }
            break;

        default:
            json_response(['success' => false, 'message' => '不支持的请求方法'], 405);
    }
} catch (Exception $e) {
    json_response([
        'success' => false,
        'message' => '操作失败: ' . $e->getMessage()
    ], 500);
}
